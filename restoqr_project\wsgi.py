"""
WSGI config for restoqr_project project.

It exposes the WSGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/5.2/howto/deployment/wsgi/
"""

import os
import sys

# ✅ أضف مسار المشروع إلى sys.path
path = '/home/<USER>/RestoQr'  # ← هذا هو مجلد المشروع ديالك
if path not in sys.path:
    sys.path.append(path)

# ✅ عيّن إعدادات Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')

# ✅ شغّل WSGI
from django.core.wsgi import get_wsgi_application
application = get_wsgi_application()


