{% extends 'restaurant/base.html' %}

{% block title %}Order Status - #{{ order.id }} - RestoQR{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 px-4 py-6">
    <div class="max-w-2xl mx-auto">
        <!-- Header -->
        <div class="text-center mb-8">
            <div class="flex items-center justify-center space-x-3 mb-4">
                <div class="w-12 h-12 bg-primary rounded-full flex items-center justify-center">
                    <i class="fas fa-utensils text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-light_text">Order Tracking</h1>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Table {{ order.table.number }}</p>
                </div>
            </div>
        </div>
        
        <!-- Order Status Progress -->
        <div class="bg-white dark:bg-dark_card rounded-xl shadow-lg p-6 mb-6">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-light_text mb-6">
                Order #{{ order.id }} Status
            </h2>
            
            <!-- Progress Steps -->
            <div class="relative">
                <!-- Progress Line -->
                <div class="absolute left-6 top-6 bottom-6 w-0.5 bg-gray-200 dark:bg-gray-700"></div>
                <div class="absolute left-6 top-6 w-0.5 bg-primary transition-all duration-500" 
                     style="height: {% if order.status == 'pending' %}25%{% elif order.status == 'confirmed' %}50%{% elif order.status == 'preparing' %}75%{% else %}100%{% endif %}"></div>
                
                <!-- Steps -->
                <div class="space-y-8">
                    <!-- Order Placed -->
                    <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 rounded-full flex items-center justify-center {% if order.status != 'pending' %}bg-primary text-white{% else %}bg-gray-200 dark:bg-gray-700 text-gray-500{% endif %}">
                            <i class="fas fa-check"></i>
                        </div>
                        <div class="flex-1">
                            <h3 class="font-semibold text-gray-900 dark:text-light_text">Order Placed</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400">{{ order.created_at|date:"H:i" }}</p>
                        </div>
                    </div>
                    
                    <!-- Order Confirmed -->
                    <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 rounded-full flex items-center justify-center {% if order.status == 'confirmed' or order.status == 'preparing' or order.status == 'ready' or order.status == 'completed' %}bg-primary text-white{% else %}bg-gray-200 dark:bg-gray-700 text-gray-500{% endif %}">
                            <i class="fas fa-clipboard-check"></i>
                        </div>
                        <div class="flex-1">
                            <h3 class="font-semibold text-gray-900 dark:text-white">Order Confirmed</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400">
                                {% if order.status == 'confirmed' or order.status == 'preparing' or order.status == 'ready' or order.status == 'completed' %}
                                    Confirmed by kitchen
                                {% else %}
                                    Waiting for confirmation
                                {% endif %}
                            </p>
                        </div>
                    </div>
                    
                    <!-- Preparing -->
                    <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 rounded-full flex items-center justify-center {% if order.status == 'preparing' or order.status == 'ready' or order.status == 'completed' %}bg-primary text-white{% else %}bg-gray-200 dark:bg-gray-700 text-gray-500{% endif %}">
                            <i class="fas fa-fire"></i>
                        </div>
                        <div class="flex-1">
                            <h3 class="font-semibold text-gray-900 dark:text-white">Preparing</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400">
                                {% if order.status == 'preparing' %}
                                    <span class="text-yellow-600 dark:text-yellow-400">
                                        <i class="fas fa-spinner fa-spin mr-1"></i>
                                        Currently being prepared
                                    </span>
                                {% elif order.status == 'ready' or order.status == 'completed' %}
                                    Preparation completed
                                {% else %}
                                    Waiting to start preparation
                                {% endif %}
                            </p>
                        </div>
                    </div>
                    
                    <!-- Ready for Pickup -->
                    <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 rounded-full flex items-center justify-center {% if order.status == 'ready' or order.status == 'completed' %}bg-primary text-white{% else %}bg-gray-200 dark:bg-gray-700 text-gray-500{% endif %}">
                            <i class="fas fa-bell"></i>
                        </div>
                        <div class="flex-1">
                            <h3 class="font-semibold text-gray-900 dark:text-white">Ready</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400">
                                {% if order.status == 'ready' %}
                                    <span class="text-green-600 dark:text-green-400">
                                        <i class="fas fa-check-circle mr-1"></i>
                                        Ready for serving!
                                    </span>
                                {% elif order.status == 'completed' %}
                                    Order served
                                {% else %}
                                    Will be ready soon
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Current Status Card -->
        <div class="bg-white dark:bg-dark_card rounded-xl shadow-lg p-6 mb-6">
            <div class="text-center">
                {% if order.status == 'pending' %}
                    <div class="w-16 h-16 bg-yellow-100 dark:bg-yellow-900 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-clock text-yellow-600 dark:text-yellow-400 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Order Received</h3>
                    <p class="text-gray-600 dark:text-gray-400">Your order is waiting for confirmation from our kitchen.</p>
                
                {% elif order.status == 'confirmed' %}
                    <div class="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-clipboard-check text-blue-600 dark:text-blue-400 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Order Confirmed</h3>
                    <p class="text-gray-600 dark:text-gray-400">Great! Our kitchen has confirmed your order and will start preparing it soon.</p>
                
                {% elif order.status == 'preparing' %}
                    <div class="w-16 h-16 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-fire text-orange-600 dark:text-orange-400 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Being Prepared</h3>
                    <p class="text-gray-600 dark:text-gray-400">Our chefs are working on your delicious meal right now!</p>
                
                {% elif order.status == 'ready' %}
                    <div class="w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse">
                        <i class="fas fa-bell text-green-600 dark:text-green-400 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Order Ready!</h3>
                    <p class="text-gray-600 dark:text-gray-400">Your order is ready and will be served to your table shortly.</p>
                
                {% elif order.status == 'completed' %}
                    <div class="w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-check-circle text-green-600 dark:text-green-400 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Order Completed</h3>
                    <p class="text-gray-600 dark:text-gray-400">Enjoy your meal! Thank you for dining with us.</p>
                {% endif %}
            </div>
        </div>
        
        <!-- Order Details -->
        <div class="bg-white dark:bg-dark_card rounded-xl shadow-lg p-6 mb-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                <i class="fas fa-receipt mr-2 text-primary"></i>
                Order Details
            </h3>
            
            <div class="space-y-3">
                <div class="flex items-center justify-between">
                    <span class="text-gray-600 dark:text-gray-400">Order Number:</span>
                    <span class="font-semibold text-gray-900 dark:text-white">#{{ order.id }}</span>
                </div>
                
                <div class="flex items-center justify-between">
                    <span class="text-gray-600 dark:text-gray-400">Table:</span>
                    <span class="font-semibold text-gray-900 dark:text-white">{{ order.table.number }}</span>
                </div>
                
                <div class="flex items-center justify-between">
                    <span class="text-gray-600 dark:text-gray-400">Order Time:</span>
                    <span class="font-semibold text-gray-900 dark:text-white">{{ order.created_at|date:"H:i" }}</span>
                </div>
                
                <div class="flex items-center justify-between">
                    <span class="text-gray-600 dark:text-gray-400">Estimated Time:</span>
                    <span class="font-semibold text-gray-900 dark:text-white">{{ order.estimated_time }} minutes</span>
                </div>
                
                <div class="flex items-center justify-between">
                    <span class="text-gray-600 dark:text-gray-400">Total Amount:</span>
                    <span class="font-bold text-primary text-lg">{{ order.total_amount }} dh</span>
                </div>
            </div>
        </div>
        
        <!-- Order Items -->
        <div class="bg-white dark:bg-dark_card rounded-xl shadow-lg p-6 mb-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                <i class="fas fa-list mr-2 text-primary"></i>
                Items Ordered
            </h3>
            
            <div class="space-y-3">
                {% for item in order.items.all %}
                <div class="flex items-center justify-between py-2 border-b border-gray-200 dark:border-gray-700 last:border-b-0">
                    <div class="flex-1">
                        <h4 class="font-medium text-gray-900 dark:text-white">{{ item.product.name }}</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            {{ item.price }} dh × {{ item.quantity }}
                        </p>
                    </div>
                    <span class="font-semibold text-gray-900 dark:text-white">
                        {{ item.subtotal }} dh
                    </span>
                </div>
                {% endfor %}
                
                {% if order.notes %}
                <div class="pt-3 border-t border-gray-200 dark:border-gray-700">
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                        <i class="fas fa-sticky-note mr-1"></i>
                        <strong>Special Instructions:</strong> {{ order.notes }}
                    </p>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="space-y-3">
            <button onclick="refreshStatus()" 
                    class="w-full bg-primary text-white text-center py-3 rounded-lg hover:bg-secondary transition-colors duration-200 font-semibold">
                <i class="fas fa-sync-alt mr-2"></i>
                Refresh Status
            </button>
            
            <a href="{% url 'restaurant:client_menu' order.table.id %}" 
               class="w-full bg-gray-500 text-white text-center py-3 rounded-lg hover:bg-gray-600 transition-colors duration-200 font-semibold block">
                <i class="fas fa-plus mr-2"></i>
                Order More Items
            </a>
            
            {% if order.status == 'completed' %}
            <button onclick="downloadInvoice()" 
                    class="w-full bg-green-500 text-white text-center py-3 rounded-lg hover:bg-green-600 transition-colors duration-200 font-semibold">
                <i class="fas fa-download mr-2"></i>
                Download Invoice (PDF)
            </button>
            {% endif %}
        </div>
    </div>
</div>

<script>
    function refreshStatus() {
        location.reload();
    }
    
    function downloadInvoice() {
        const link = document.createElement('a');
        link.href = '{% url "restaurant:download_invoice" order.id %}';
        link.download = 'invoice-{{ order.id }}.pdf';
        link.click();
    }
    
    // Auto-refresh every 30 seconds
    setInterval(() => {
        fetch(`/api/order-status/{{ order.id }}/`)
            .then(response => response.json())
            .then(data => {
                if (data.status !== '{{ order.status }}') {
                    location.reload();
                }
            })
            .catch(error => console.log('Status check failed:', error));
    }, 30000);
    
    // Show notification when order status changes
    {% if order.status == 'ready' %}
    if ('Notification' in window && Notification.permission === 'granted') {
        new Notification('Order Ready!', {
            body: 'Your order #{{ order.id }} is ready to be served.',
            icon: '/static/images/logo.png'
        });
    }
    {% endif %}
</script>
{% endblock %}

