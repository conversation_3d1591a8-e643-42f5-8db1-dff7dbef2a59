
@csrf_exempt
@require_http_methods(["POST"])
def api_create_order(request):
    """API endpoint to create a new order"""
    try:
        data = json.loads(request.body)
        table_id = data.get(\'table_id\')
        items = data.get(\'items\', [])
        customer_notes = data.get(\'customer_notes\', \'\')
        
        if not table_id or not items:
            return JsonResponse({
                \'success\': False,
                \'error\': \'Table ID and items are required\'
            }, status=400)
        
        table = get_object_or_404(Table, id=table_id, is_active=True)
        
        # Create order
        order = Order.objects.create(
            table=table,
            customer_notes=customer_notes,
            status=\'pending\'
        )
        
        # Create order items
        for item_data in items:
            product = get_object_or_404(Product, id=item_data["product_id"])
            quantity = int(item_data["quantity"])
            notes = item_data.get("notes", "")
            
            # Create and save the order item
            order_item = OrderItem.objects.create(
                order=order,
                product=product,
                quantity=quantity,
                notes=notes
            )
            # The save method will automatically set unit_price and subtotal
        
        # Refresh the order from database to get updated items
        order.refresh_from_db()
        
        # Update order total and estimated ready time AFTER all items are saved
        order.calculate_total()
        order.calculate_estimated_ready_time()
        
        return JsonResponse({
            \'success\': True,
            \'order_id\': str(order.id),
            \'total_amount\': float(order.total_amount),
            \'estimated_ready_time\': order.estimated_ready_time.isoformat() if order.estimated_ready_time else None,
        })
    
    except Exception as e:
        return JsonResponse({
            \'success\': False,
            \'error\': str(e)
        }, status=400)


@login_required
def download_invoice(request, order_id):
    """Download PDF invoice for an order"""
    try:
        order = Order.objects.get(id=order_id)
        save_invoice_pdf(order) # Save PDF to server for verification
        return create_invoice_response(order)
    except Order.DoesNotExist:
        return HttpResponse("Order not found.", status=404)

def client_download_invoice(request, order_id):
    """Download PDF invoice for an order (client access)"""
    try:
        order = Order.objects.get(id=order_id)
        return create_invoice_response(order)
    except Order.DoesNotExist:
        return HttpResponse(\'Order not found.\', status=404)


