{% extends 'restaurant/base.html' %}

{% block title %}Server Error - RestoQR{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 dark:bg-dark_background flex items-center justify-center px-4">
    <div class="max-w-md w-full text-center">
        <!-- Error Illustration -->
        <div class="mb-8">
            <div class="mx-auto w-32 h-32 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mb-6">
                <i class="fas fa-server text-red-600 dark:text-red-400 text-5xl"></i>
            </div>
            
            <h1 class="text-6xl font-bold text-gray-900 dark:text-light_text mb-4">500</h1>
            <h2 class="text-2xl font-semibold text-gray-700 dark:text-gray-300 mb-4">Server Error</h2>
            <p class="text-gray-600 dark:text-gray-400 mb-8">
                Something went wrong on our end. We're working to fix this issue.
            </p>
        </div>
        
        <!-- Status Information -->
        <div class="bg-white dark:bg-dark_card rounded-xl shadow-sm p-6 mb-8">
            <div class="flex items-center justify-center mb-4">
                <div class="flex space-x-2">
                    <div class="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                    <div class="w-3 h-3 bg-yellow-500 rounded-full animate-pulse" style="animation-delay: 0.2s"></div>
                    <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse" style="animation-delay: 0.4s"></div>
                </div>
            </div>
            <p class="text-sm text-gray-600 dark:text-gray-400">
                Our team has been notified and is working on a solution.
            </p>
        </div>
        
        <!-- Action Buttons -->
        <div class="space-y-4">
            <button onclick="location.reload()" 
                    class="inline-flex items-center px-6 py-3 bg-primary text-white rounded-lg hover:bg-secondary transition-colors duration-200 font-medium">
                <i class="fas fa-redo mr-2"></i>
                Try Again
            </button>
            
            <div class="flex justify-center space-x-4">
                <a href="{% url 'restaurant:admin_dashboard' %}" 
                   class="inline-flex items-center px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200">
                    <i class="fas fa-home mr-2"></i>
                    Dashboard
                </a>
                
                <button onclick="history.back()" 
                        class="inline-flex items-center px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Go Back
                </button>
            </div>
        </div>
        
        <!-- Technical Details (for development) -->
        {% if debug %}
        <div class="mt-8 p-4 bg-red-50 dark:bg-red-900 rounded-lg text-left">
            <h4 class="font-semibold text-red-800 dark:text-red-200 mb-2">Technical Details:</h4>
            <pre class="text-xs text-red-700 dark:text-red-300 overflow-auto">{{ exception }}</pre>
        </div>
        {% endif %}
        
        <!-- Contact Support -->
        <div class="mt-8 p-6 bg-white dark:bg-dark_card rounded-xl shadow-sm">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-light_text mb-3">Still Having Issues?</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                If the problem persists, please contact our technical support team.
            </p>
            <div class="flex justify-center space-x-4 text-sm">
                <a href="mailto:<EMAIL>" class="text-primary hover:text-secondary">
                    <i class="fas fa-envelope mr-1"></i>
                    Technical Support
                </a>
                <span class="text-gray-400">|</span>
                <a href="tel:+1234567890" class="text-primary hover:text-secondary">
                    <i class="fas fa-phone mr-1"></i>
                    Emergency Line
                </a>
            </div>
        </div>
    </div>
</div>

<style>
    .server-pulse {
        animation: serverPulse 2s infinite;
    }
    
    @keyframes serverPulse {
        0%, 100% {
            transform: scale(1);
            opacity: 1;
        }
        50% {
            transform: scale(1.05);
            opacity: 0.8;
        }
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add server pulse animation
        const serverIcon = document.querySelector('.fas.fa-server');
        if (serverIcon) {
            serverIcon.parentElement.classList.add('server-pulse');
        }
        
        // Auto-retry after 30 seconds
        setTimeout(() => {
            const retryBtn = document.querySelector('button[onclick="location.reload()"]');
            if (retryBtn) {
                retryBtn.innerHTML = '<i class="fas fa-redo mr-2"></i>Auto-retrying...';
                retryBtn.disabled = true;
                setTimeout(() => location.reload(), 2000);
            }
        }, 30000);
        
        // Log error for monitoring
        console.error('500 Server Error:', window.location.pathname);
    });
</script>
{% endblock %}