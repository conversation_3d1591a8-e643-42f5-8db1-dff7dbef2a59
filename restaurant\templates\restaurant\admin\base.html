{% extends 'restaurant/base.html' %}

{% block extra_css %}
<style>
    /* Custom animations and effects */
    .slide-in {
        animation: slideIn 0.3s ease-out;
    }
    
    @keyframes slideIn {
        from { transform: translateX(-100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    .fade-in-up {
        animation: fadeInUp 0.5s ease-out;
    }
    
    @keyframes fadeInUp {
        from { transform: translateY(20px); opacity: 0; }
        to { transform: translateY(0); opacity: 1; }
    }
    
    .hover-lift {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }
    
    .hover-lift:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }
    
    .glass-morphism {
        backdrop-filter: blur(16px) saturate(180%);
        background-color: rgba(255, 255, 255, 0.75);
        border: 1px solid rgba(209, 213, 219, 0.3);
    }
    
    .dark .glass-morphism {
        background-color: rgba(31, 41, 55, 0.75);
        border: 1px solid rgba(75, 85, 99, 0.3);
    }
    
    .gradient-border {
        background: linear-gradient(45deg, #3B82F6, #8B5CF6, #06B6D4);
        padding: 2px;
        border-radius: 12px;
    }
    
    .gradient-border > div {
        background: white;
        border-radius: 10px;
    }
    
    .dark .gradient-border > div {
        background: #1F2937;
    }
    
    .notification-dot {
        animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    }
    
    .sidebar-item {
        position: relative;
        overflow: hidden;
    }
    
    .sidebar-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }
    
    .sidebar-item:hover::before {
        left: 100%;
    }
    
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        position: relative;
        overflow: hidden;
    }
    
    .stats-card::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 100%;
        height: 100%;
        background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
        animation: float 6s ease-in-out infinite;
    }
    
    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(180deg); }
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
    <!-- Sidebar -->
    <div id="sidebar" class="fixed inset-y-0 left-0 z-50 w-72 transform transition-transform duration-300 ease-in-out lg:translate-x-0 -translate-x-full">
        <!-- Sidebar Background with Gradient -->
        <div class="h-full glass-morphism border-r border-gray-200/50 dark:border-gray-700/50">
            <!-- Logo Section -->
            <div class="flex items-center justify-center h-20 bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 relative overflow-hidden">
                <div class="absolute inset-0 bg-black/10"></div>
                <div class="relative z-10 flex items-center space-x-3">
                    <div class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center backdrop-blur-sm">
                        <i class="fas fa-qrcode text-white text-xl"></i>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold text-white">RestoQR</h1>
                        <p class="text-xs text-white/80">Admin Panel</p>
                    </div>
                </div>
                <!-- Animated Background Elements -->
                <div class="absolute top-0 left-0 w-full h-full">
                    <div class="absolute top-2 left-4 w-2 h-2 bg-white/30 rounded-full animate-ping"></div>
                    <div class="absolute bottom-4 right-6 w-1 h-1 bg-white/40 rounded-full animate-pulse"></div>
                </div>
            </div>
            
            <!-- Navigation Menu -->
            <nav class="mt-8 px-4">
                <div class="space-y-2">
                    <!-- Dashboard -->
                    <a href="{% url 'restaurant:admin_dashboard' %}" 
                       class="sidebar-item group flex items-center px-4 py-3 text-gray-700 dark:text-gray-200 rounded-xl hover:bg-gradient-to-r hover:from-blue-500 hover:to-purple-600 hover:text-white transition-all duration-300 {% if request.resolver_match.url_name == 'admin_dashboard' %}bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg{% endif %}">
                        <div class="w-10 h-10 rounded-lg bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center mr-3 group-hover:bg-white/20 transition-colors duration-300">
                            <i class="fas fa-chart-line text-blue-600 dark:text-blue-400 group-hover:text-white"></i>
                        </div>
                        <div>
                            <div class="font-medium">Dashboard</div>
                            <div class="text-xs opacity-75">Vue d'ensemble</div>
                        </div>
                        {% if request.resolver_match.url_name == 'admin_dashboard' %}
                        <div class="ml-auto w-2 h-2 bg-white rounded-full"></div>
                        {% endif %}
                    </a>
                    
                    <!-- Products -->
                    <a href="{% url 'restaurant:product_list' %}" 
                       class="sidebar-item group flex items-center px-4 py-3 text-gray-700 dark:text-gray-200 rounded-xl hover:bg-gradient-to-r hover:from-green-500 hover:to-emerald-600 hover:text-white transition-all duration-300 {% if 'product' in request.resolver_match.url_name %}bg-gradient-to-r from-green-500 to-emerald-600 text-white shadow-lg{% endif %}">
                        <div class="w-10 h-10 rounded-lg bg-green-100 dark:bg-green-900/30 flex items-center justify-center mr-3 group-hover:bg-white/20 transition-colors duration-300">
                            <i class="fas fa-utensils text-green-600 dark:text-green-400 group-hover:text-white"></i>
                        </div>
                        <div>
                            <div class="font-medium">Produits</div>
                            <div class="text-xs opacity-75">Menu & Articles</div>
                        </div>
                        {% if 'product' in request.resolver_match.url_name %}
                        <div class="ml-auto w-2 h-2 bg-white rounded-full"></div>
                        {% endif %}
                    </a>
                    
                    <!-- Categories -->
                    <a href="{% url 'restaurant:category_list' %}" 
                       class="sidebar-item group flex items-center px-4 py-3 text-gray-700 dark:text-gray-200 rounded-xl hover:bg-gradient-to-r hover:from-purple-500 hover:to-pink-600 hover:text-white transition-all duration-300 {% if 'category' in request.resolver_match.url_name %}bg-gradient-to-r from-purple-500 to-pink-600 text-white shadow-lg{% endif %}">
                        <div class="w-10 h-10 rounded-lg bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center mr-3 group-hover:bg-white/20 transition-colors duration-300">
                            <i class="fas fa-tags text-purple-600 dark:text-purple-400 group-hover:text-white"></i>
                        </div>
                        <div>
                            <div class="font-medium">Catégories</div>
                            <div class="text-xs opacity-75">Organisation</div>
                        </div>
                        {% if 'category' in request.resolver_match.url_name %}
                        <div class="ml-auto w-2 h-2 bg-white rounded-full"></div>
                        {% endif %}
                    </a>
                    
                    <!-- Tables -->
                    <a href="{% url 'restaurant:table_list' %}" 
                       class="sidebar-item group flex items-center px-4 py-3 text-gray-700 dark:text-gray-200 rounded-xl hover:bg-gradient-to-r hover:from-orange-500 hover:to-red-600 hover:text-white transition-all duration-300 {% if 'table' in request.resolver_match.url_name %}bg-gradient-to-r from-orange-500 to-red-600 text-white shadow-lg{% endif %}">
                        <div class="w-10 h-10 rounded-lg bg-orange-100 dark:bg-orange-900/30 flex items-center justify-center mr-3 group-hover:bg-white/20 transition-colors duration-300">
                            <i class="fas fa-chair text-orange-600 dark:text-orange-400 group-hover:text-white"></i>
                        </div>
                        <div>
                            <div class="font-medium">Tables</div>
                            <div class="text-xs opacity-75">QR Codes</div>
                        </div>
                        {% if 'table' in request.resolver_match.url_name %}
                        <div class="ml-auto w-2 h-2 bg-white rounded-full"></div>
                        {% endif %}
                    </a>
                    
                    <!-- Orders -->
                    <a href="{% url 'restaurant:order_list' %}" 
                       class="sidebar-item group flex items-center px-4 py-3 text-gray-700 dark:text-gray-200 rounded-xl hover:bg-gradient-to-r hover:from-indigo-500 hover:to-blue-600 hover:text-white transition-all duration-300 {% if 'order' in request.resolver_match.url_name %}bg-gradient-to-r from-indigo-500 to-blue-600 text-white shadow-lg{% endif %}">
                        <div class="w-10 h-10 rounded-lg bg-indigo-100 dark:bg-indigo-900/30 flex items-center justify-center mr-3 group-hover:bg-white/20 transition-colors duration-300">
                            <i class="fas fa-receipt text-indigo-600 dark:text-indigo-400 group-hover:text-white"></i>
                        </div>
                        <div>
                            <div class="font-medium">Commandes</div>
                            <div class="text-xs opacity-75">Gestion</div>
                        </div>
                        <div class="ml-auto">
                            <span class="notification-dot inline-flex items-center justify-center w-5 h-5 text-xs font-bold text-white bg-red-500 rounded-full">3</span>
                        </div>
                        {% if 'order' in request.resolver_match.url_name %}
                        <div class="ml-2 w-2 h-2 bg-white rounded-full"></div>
                        {% endif %}
                    </a>
                </div>
                
                <!-- Settings Section -->
                <div class="mt-8 pt-6 border-t border-gray-200/50 dark:border-gray-700/50">
                    <div class="space-y-2">
                        <a href="{% url 'restaurant:restaurant_settings' %}" 
                           class="sidebar-item group flex items-center px-4 py-3 text-gray-700 dark:text-gray-200 rounded-xl hover:bg-gradient-to-r hover:from-gray-500 hover:to-gray-600 hover:text-white transition-all duration-300 {% if request.resolver_match.url_name == 'restaurant_settings' %}bg-gradient-to-r from-gray-500 to-gray-600 text-white shadow-lg{% endif %}">
                            <div class="w-10 h-10 rounded-lg bg-gray-100 dark:bg-gray-900/30 flex items-center justify-center mr-3 group-hover:bg-white/20 transition-colors duration-300">
                                <i class="fas fa-cog text-gray-600 dark:text-gray-400 group-hover:text-white"></i>
                            </div>
                            <div>
                                <div class="font-medium">Paramètres</div>
                                <div class="text-xs opacity-75">Configuration</div>
                            </div>
                        </a>
                        
                        <a href="{% url 'restaurant:admin_profile' %}" 
                           class="sidebar-item group flex items-center px-4 py-3 text-gray-700 dark:text-gray-200 rounded-xl hover:bg-gradient-to-r hover:from-teal-500 hover:to-cyan-600 hover:text-white transition-all duration-300">
                            <div class="w-10 h-10 rounded-lg bg-teal-100 dark:bg-teal-900/30 flex items-center justify-center mr-3 group-hover:bg-white/20 transition-colors duration-300">
                                <i class="fas fa-user text-teal-600 dark:text-teal-400 group-hover:text-white"></i>
                            </div>
                            <div>
                                <div class="font-medium">Profil</div>
                                <div class="text-xs opacity-75">Mon compte</div>
                            </div>
                        </a>
                    </div>
                </div>
                
                <!-- Logout Section -->
                <div class="mt-8 pt-6 border-t border-gray-200/50 dark:border-gray-700/50">
                    <a href="{% url 'restaurant:admin_logout' %}" 
                       class="sidebar-item group flex items-center px-4 py-3 text-red-600 dark:text-red-400 rounded-xl hover:bg-gradient-to-r hover:from-red-500 hover:to-red-600 hover:text-white transition-all duration-300">
                        <div class="w-10 h-10 rounded-lg bg-red-100 dark:bg-red-900/30 flex items-center justify-center mr-3 group-hover:bg-white/20 transition-colors duration-300">
                            <i class="fas fa-sign-out-alt text-red-600 dark:text-red-400 group-hover:text-white"></i>
                        </div>
                        <div>
                            <div class="font-medium">Déconnexion</div>
                            <div class="text-xs opacity-75">Se déconnecter</div>
                        </div>
                    </a>
                </div>
            </nav>
        </div>
    </div>
    
    <!-- Main Content Area -->
    <div class="lg:ml-72 transition-all duration-300">
        <!-- Top Header -->
        <header class="glass-morphism border-b border-gray-200/50 dark:border-gray-700/50 sticky top-0 z-40">
            <div class="flex items-center justify-between px-6 py-4">
                <!-- Left Section -->
                <div class="flex items-center space-x-4">
                    <!-- Mobile Menu Button -->
                    <button id="sidebar-toggle" class="lg:hidden p-2 rounded-xl bg-white/50 dark:bg-gray-800/50 text-gray-600 dark:text-gray-300 hover:bg-white dark:hover:bg-gray-800 transition-all duration-200">
                        <i class="fas fa-bars text-lg"></i>
                    </button>
                    
                    <!-- Page Title -->
                    <div>
                        <h1 class="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">
                            {% block page_title %}Dashboard{% endblock %}
                        </h1>
                        <p class="text-sm text-gray-500 dark:text-gray-400">
                            {% block page_subtitle %}Vue d'ensemble de votre restaurant{% endblock %}
                        </p>
                    </div>
                </div>
                
                <!-- Right Section -->
                <div class="flex items-center space-x-4">
                    <!-- Quick Actions -->
                    <div class="hidden md:flex items-center space-x-2">
                        <button class="p-2 rounded-xl bg-white/50 dark:bg-gray-800/50 text-gray-600 dark:text-gray-300 hover:bg-white dark:hover:bg-gray-800 transition-all duration-200 hover-lift">
                            <i class="fas fa-bell text-lg"></i>
                            <span class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full notification-dot"></span>
                        </button>
                        
                        <button class="p-2 rounded-xl bg-white/50 dark:bg-gray-800/50 text-gray-600 dark:text-gray-300 hover:bg-white dark:hover:bg-gray-800 transition-all duration-200 hover-lift">
                            <i class="fas fa-search text-lg"></i>
                        </button>
                    </div>
                    
                    <!-- Dark Mode Toggle -->
                    <button onclick="toggleDarkMode()" class="p-2 rounded-xl bg-white/50 dark:bg-gray-800/50 text-gray-600 dark:text-gray-300 hover:bg-white dark:hover:bg-gray-800 transition-all duration-200 hover-lift">
                        <i class="fas fa-moon dark:hidden text-lg"></i>
                        <i class="fas fa-sun hidden dark:inline text-lg"></i>
                    </button>
                    
                    <!-- User Profile -->
                    <div class="flex items-center space-x-3 px-3 py-2 rounded-xl bg-white/50 dark:bg-gray-800/50 hover:bg-white dark:hover:bg-gray-800 transition-all duration-200 hover-lift">
                        <div class="w-10 h-10 rounded-xl bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                            <i class="fas fa-user text-white text-sm"></i>
                        </div>
                        <div class="hidden sm:block">
                            <div class="text-sm font-medium text-gray-900 dark:text-white">{{ user.get_full_name|default:user.username }}</div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">Administrateur</div>
                        </div>
                    </div>
                </div>
            </div>
        </header>
        
        <!-- Page Content -->
        <main class="p-6">
            <div class="fade-in-up">
                {% block admin_content %}
                <div class="text-center py-20">
                    <div class="w-24 h-24 mx-auto mb-6 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                        <i class="fas fa-rocket text-white text-3xl"></i>
                    </div>
                    <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">Bienvenue dans RestoQR</h2>
                    <p class="text-gray-600 dark:text-gray-400 max-w-md mx-auto">
                        Votre tableau de bord moderne pour gérer votre restaurant avec style et efficacité.
                    </p>
                </div>
                {% endblock %}
            </div>
        </main>
    </div>
    
    <!-- Mobile Sidebar Overlay -->
    <div id="sidebar-overlay" class="fixed inset-0 z-40 bg-black/50 backdrop-blur-sm lg:hidden hidden transition-opacity duration-300"></div>
</div>

<!-- Enhanced JavaScript -->
<script>
    // Sidebar functionality
    const sidebar = document.getElementById('sidebar');
    const sidebarToggle = document.getElementById('sidebar-toggle');
    const sidebarOverlay = document.getElementById('sidebar-overlay');
    
    function toggleSidebar() {
        sidebar.classList.toggle('-translate-x-full');
        sidebarOverlay.classList.toggle('hidden');
        document.body.classList.toggle('overflow-hidden');
    }
    
    sidebarToggle?.addEventListener('click', toggleSidebar);
    sidebarOverlay?.addEventListener('click', toggleSidebar);
    
    // Close sidebar on escape key
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && !sidebar.classList.contains('-translate-x-full')) {
            toggleSidebar();
        }
    });
    
    // Enhanced SweetAlert2 Configuration
    const Toast = Swal.mixin({
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 4000,
        timerProgressBar: true,
        background: 'rgba(255, 255, 255, 0.95)',
        backdrop: 'rgba(0, 0, 0, 0.1)',
        didOpen: (toast) => {
            toast.addEventListener('mouseenter', Swal.stopTimer);
            toast.addEventListener('mouseleave', Swal.resumeTimer);
        }
    });
    
    // Show Django messages with enhanced styling
    {% if messages %}
        {% for message in messages %}
            Toast.fire({
                icon: '{% if message.tags == "success" %}success{% elif message.tags == "error" %}error{% elif message.tags == "warning" %}warning{% else %}info{% endif %}',
                title: '{{ message|escapejs }}',
                customClass: {
                    popup: 'glass-morphism border border-gray-200/50'
                }
            });
        {% endfor %}
    {% endif %}
    
    // Enhanced confirmation dialogs
    function confirmDelete(title, text, url) {
        Swal.fire({
            title: title,
            text: text,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#ef4444',
            cancelButtonColor: '#6b7280',
            confirmButtonText: '<i class="fas fa-trash mr-2"></i>Supprimer',
            cancelButtonText: '<i class="fas fa-times mr-2"></i>Annuler',
            background: 'rgba(255, 255, 255, 0.95)',
            backdrop: 'rgba(0, 0, 0, 0.4)',
            customClass: {
                popup: 'glass-morphism border border-gray-200/50'
            }
        }).then((result) => {
            if (result.isConfirmed) {
                // Show loading
                Swal.fire({
                    title: 'Suppression en cours...',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });
                window.location.href = url;
            }
        });
    }
    
    // Success notification with animation
    function showSuccess(title, text) {
        Swal.fire({
            icon: 'success',
            title: title,
            text: text,
            timer: 3000,
            showConfirmButton: false,
            background: 'rgba(255, 255, 255, 0.95)',
            customClass: {
                popup: 'glass-morphism border border-green-200/50'
            }
        });
    }
    
    // Error notification with animation
    function showError(title, text) {
        Swal.fire({
            icon: 'error',
            title: title,
            text: text,
            background: 'rgba(255, 255, 255, 0.95)',
            customClass: {
                popup: 'glass-morphism border border-red-200/50'
            }
        });
    }
    
    // Auto-refresh functionality with visual indicator
    let refreshInterval;
    function startAutoRefresh() {
        refreshInterval = setInterval(() => {
            // Add subtle refresh indicator
            const refreshIndicator = document.createElement('div');
            refreshIndicator.className = 'fixed top-4 right-4 z-50 bg-blue-500 text-white px-3 py-1 rounded-full text-xs opacity-75';
            refreshIndicator.innerHTML = '<i class="fas fa-sync-alt animate-spin mr-1"></i>Actualisation...';
            document.body.appendChild(refreshIndicator);
            
            setTimeout(() => {
                refreshIndicator.remove();
            }, 2000);
        }, 30000);
    }
    
    // Initialize auto-refresh
    startAutoRefresh();
    
    // Smooth scroll for internal links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Add loading states to forms
    document.querySelectorAll('form').forEach(form => {
        form.addEventListener('submit', function() {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner animate-spin mr-2"></i>Traitement...';
            }
        });
    });
    
    // Initialize tooltips for elements with title attribute
    document.querySelectorAll('[title]').forEach(element => {
        element.addEventListener('mouseenter', function() {
            const tooltip = document.createElement('div');
            tooltip.className = 'fixed z-50 px-2 py-1 text-xs text-white bg-gray-900 rounded shadow-lg pointer-events-none';
            tooltip.textContent = this.getAttribute('title');
            document.body.appendChild(tooltip);
            
            const rect = this.getBoundingClientRect();
            tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
            tooltip.style.top = rect.top - tooltip.offsetHeight - 5 + 'px';
            
            this.addEventListener('mouseleave', () => {
                tooltip.remove();
            }, { once: true });
        });
    });
</script>
{% endblock %}