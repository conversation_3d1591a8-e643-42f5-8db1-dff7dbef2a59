<!DOCTYPE html>
<html lang="fr" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RestoQR - L'Avenir de la Restauration</title>
    <meta name="description" content="RestoQR - Solution révolutionnaire de menu QR code pour restaurants modernes. Commandes sans contact, gestion intelligente, expérience client exceptionnelle.">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Custom Tailwind Config -->
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                        'display': ['Poppins', 'Inter', 'system-ui', 'sans-serif'],
                    },
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            900: '#1e3a8a',
                        },
                        secondary: {
                            500: '#8b5cf6',
                            600: '#7c3aed',
                        },
                        accent: {
                            500: '#f59e0b',
                            600: '#d97706',
                        },
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.6s ease-out',
                        'fade-in-up': 'fadeInUp 0.6s ease-out',
                        'fade-in-down': 'fadeInDown 0.6s ease-out',
                        'slide-in-left': 'slideInLeft 0.6s ease-out',
                        'slide-in-right': 'slideInRight 0.6s ease-out',
                        'bounce-slow': 'bounce 3s infinite',
                        'pulse-slow': 'pulse 4s infinite',
                        'float': 'float 6s ease-in-out infinite',
                        'glow': 'glow 2s ease-in-out infinite alternate',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                        fadeInUp: {
                            '0%': { opacity: '0', transform: 'translateY(30px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' },
                        },
                        fadeInDown: {
                            '0%': { opacity: '0', transform: 'translateY(-30px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' },
                        },
                        slideInLeft: {
                            '0%': { opacity: '0', transform: 'translateX(-50px)' },
                            '100%': { opacity: '1', transform: 'translateX(0)' },
                        },
                        slideInRight: {
                            '0%': { opacity: '0', transform: 'translateX(50px)' },
                            '100%': { opacity: '1', transform: 'translateX(0)' },
                        },
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-20px)' },
                        },
                        glow: {
                            '0%': { boxShadow: '0 0 20px rgba(59, 130, 246, 0.5)' },
                            '100%': { boxShadow: '0 0 30px rgba(59, 130, 246, 0.8)' },
                        },
                    },
                },
            },
        }
    </script>
    
    <!-- Custom Styles -->
    <style>
        .glass-morphism {
            backdrop-filter: blur(16px) saturate(180%);
            background-color: rgba(255, 255, 255, 0.75);
            border: 1px solid rgba(209, 213, 219, 0.3);
        }
        
        .dark .glass-morphism {
            background-color: rgba(31, 41, 55, 0.75);
            border: 1px solid rgba(75, 85, 99, 0.3);
        }
        
        .gradient-text {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .hero-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .feature-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .feature-card:hover {
            transform: translateY(-8px) scale(1.02);
        }
        
        .parallax-bg {
            background-attachment: fixed;
            background-position: center;
            background-repeat: no-repeat;
            background-size: cover;
        }
        
        .text-shadow {
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .animate-on-scroll {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease-out;
        }
        
        .animate-on-scroll.animate {
            opacity: 1;
            transform: translateY(0);
        }
        
        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: #f1f5f9;
        }
        
        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        }
    </style>
</head>

<body class="font-sans bg-white dark:bg-gray-900 text-gray-900 dark:text-white overflow-x-hidden">
    <!-- Navigation -->
    <nav class="fixed top-0 left-0 right-0 z-50 glass-morphism border-b border-gray-200/50 dark:border-gray-700/50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <!-- Logo -->
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                        <i class="fas fa-qrcode text-white text-lg"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold gradient-text">RestoQR</h1>
                        <p class="text-xs text-gray-500 dark:text-gray-400">Smart Restaurant</p>
                    </div>
                </div>
                
                <!-- Navigation Links -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#home" class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-300">Accueil</a>
                    <a href="#features" class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-300">Fonctionnalités</a>
                    <a href="#benefits" class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-300">Avantages</a>
                    <a href="#contact" class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-300">Contact</a>
                </div>
                
                <!-- CTA Buttons -->
                <div class="flex items-center space-x-4">
                    <button onclick="toggleDarkMode()" class="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors duration-300">
                        <i class="fas fa-moon dark:hidden"></i>
                        <i class="fas fa-sun hidden dark:inline"></i>
                    </button>
                    
                    <a href="{% url 'restaurant:admin_login' %}" class="hidden sm:inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-300 transform hover:scale-105">
                        <i class="fas fa-sign-in-alt mr-2"></i>
                        Connexion
                    </a>
                    
                    <!-- Mobile Menu Button -->
                    <button id="mobile-menu-btn" class="md:hidden p-2 rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Mobile Menu -->
        <div id="mobile-menu" class="md:hidden hidden bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700">
            <div class="px-4 py-4 space-y-3">
                <a href="#home" class="block text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-300">Accueil</a>
                <a href="#features" class="block text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-300">Fonctionnalités</a>
                <a href="#benefits" class="block text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-300">Avantages</a>
                <a href="#contact" class="block text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-300">Contact</a>
                <a href="{% url 'restaurant:admin_login' %}" class="block w-full text-center px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg">
                    Connexion Admin
                </a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="relative min-h-screen flex items-center justify-center overflow-hidden">
        <!-- Animated Background -->
        <div class="absolute inset-0 hero-gradient">
            <div class="absolute inset-0 bg-black/20"></div>
            <!-- Floating Elements -->
            <div class="absolute top-20 left-10 w-20 h-20 bg-white/10 rounded-full animate-float"></div>
            <div class="absolute top-40 right-20 w-16 h-16 bg-white/10 rounded-full animate-float" style="animation-delay: 2s;"></div>
            <div class="absolute bottom-40 left-20 w-12 h-12 bg-white/10 rounded-full animate-float" style="animation-delay: 4s;"></div>
            <div class="absolute bottom-20 right-10 w-24 h-24 bg-white/10 rounded-full animate-float" style="animation-delay: 1s;"></div>
        </div>
        
        <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-white">
            <div class="animate-fade-in-down">
                <h1 class="text-4xl sm:text-5xl lg:text-7xl font-display font-black mb-6 text-shadow">
                    L'Avenir de la
                    <span class="block bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
                        Restauration
                    </span>
                </h1>
                
                <p class="text-xl sm:text-2xl lg:text-3xl font-light mb-8 max-w-4xl mx-auto text-shadow">
                    Révolutionnez votre restaurant avec RestoQR - La solution complète de menu QR code et gestion intelligente
                </p>
                
                <div class="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6 mb-12">
                    <a href="{% url 'restaurant:admin_login' %}" class="group inline-flex items-center px-8 py-4 bg-white text-gray-900 rounded-full font-semibold text-lg hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 hover:shadow-2xl">
                        <i class="fas fa-rocket mr-3 text-blue-600 group-hover:animate-bounce"></i>
                        Commencer Maintenant
                        <i class="fas fa-arrow-right ml-3 group-hover:translate-x-1 transition-transform duration-300"></i>
                    </a>
                    
                    <button onclick="scrollToDemo()" class="group inline-flex items-center px-8 py-4 border-2 border-white text-white rounded-full font-semibold text-lg hover:bg-white hover:text-gray-900 transition-all duration-300 transform hover:scale-105">
                        <i class="fas fa-play mr-3 group-hover:animate-pulse"></i>
                        Voir la Démo
                    </button>
                </div>
            </div>
            
            <!-- Stats -->
            <div class="animate-fade-in-up grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
                <div class="text-center">
                    <div class="text-3xl sm:text-4xl font-bold mb-2" data-count="500">0</div>
                    <div class="text-sm sm:text-base text-white/80">Restaurants Actifs</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl sm:text-4xl font-bold mb-2" data-count="50000">0</div>
                    <div class="text-sm sm:text-base text-white/80">Commandes Traitées</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl sm:text-4xl font-bold mb-2" data-count="98">0</div>
                    <div class="text-sm sm:text-base text-white/80">% Satisfaction</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl sm:text-4xl font-bold mb-2" data-count="24">0</div>
                    <div class="text-sm sm:text-base text-white/80">Support 24/7</div>
                </div>
            </div>
        </div>
        
        <!-- Scroll Indicator -->
        <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
            <i class="fas fa-chevron-down text-white text-2xl"></i>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-20 bg-gray-50 dark:bg-gray-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16 animate-on-scroll">
                <h2 class="text-4xl sm:text-5xl font-display font-bold mb-6 gradient-text">
                    Fonctionnalités Innovantes
                </h2>
                <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                    Découvrez comment RestoQR transforme l'expérience de vos clients et optimise la gestion de votre restaurant
                </p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Feature 1 -->
                <div class="feature-card bg-white dark:bg-gray-900 rounded-2xl p-8 shadow-lg hover:shadow-2xl animate-on-scroll">
                    <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mb-6">
                        <i class="fas fa-qrcode text-white text-2xl"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-4 text-gray-900 dark:text-white">Menu QR Code</h3>
                    <p class="text-gray-600 dark:text-gray-300 mb-6">
                        Générez automatiquement des QR codes uniques pour chaque table. Vos clients accèdent instantanément au menu depuis leur smartphone.
                    </p>
                    <ul class="space-y-2 text-sm text-gray-500 dark:text-gray-400">
                        <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>QR codes personnalisés</li>
                        <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Menu interactif</li>
                        <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Mise à jour en temps réel</li>
                    </ul>
                </div>
                
                <!-- Feature 2 -->
                <div class="feature-card bg-white dark:bg-gray-900 rounded-2xl p-8 shadow-lg hover:shadow-2xl animate-on-scroll" style="animation-delay: 0.2s;">
                    <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mb-6">
                        <i class="fas fa-shopping-cart text-white text-2xl"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-4 text-gray-900 dark:text-white">Commandes Sans Contact</h3>
                    <p class="text-gray-600 dark:text-gray-300 mb-6">
                        Les clients passent commande directement depuis leur téléphone. Fini les attentes et les malentendus !
                    </p>
                    <ul class="space-y-2 text-sm text-gray-500 dark:text-gray-400">
                        <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Commande instantanée</li>
                        <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Panier intelligent</li>
                        <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Confirmation automatique</li>
                    </ul>
                </div>
                
                <!-- Feature 3 -->
                <div class="feature-card bg-white dark:bg-gray-900 rounded-2xl p-8 shadow-lg hover:shadow-2xl animate-on-scroll" style="animation-delay: 0.4s;">
                    <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mb-6">
                        <i class="fas fa-chart-line text-white text-2xl"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-4 text-gray-900 dark:text-white">Analytics Avancées</h3>
                    <p class="text-gray-600 dark:text-gray-300 mb-6">
                        Suivez vos performances en temps réel avec des tableaux de bord détaillés et des rapports personnalisés.
                    </p>
                    <ul class="space-y-2 text-sm text-gray-500 dark:text-gray-400">
                        <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Rapports détaillés</li>
                        <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Métriques en temps réel</li>
                        <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Prédictions intelligentes</li>
                    </ul>
                </div>
                
                <!-- Feature 4 -->
                <div class="feature-card bg-white dark:bg-gray-900 rounded-2xl p-8 shadow-lg hover:shadow-2xl animate-on-scroll" style="animation-delay: 0.6s;">
                    <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center mb-6">
                        <i class="fas fa-utensils text-white text-2xl"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-4 text-gray-900 dark:text-white">Gestion Complète</h3>
                    <p class="text-gray-600 dark:text-gray-300 mb-6">
                        Gérez vos produits, catégories, tables et commandes depuis une interface moderne et intuitive.
                    </p>
                    <ul class="space-y-2 text-sm text-gray-500 dark:text-gray-400">
                        <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Interface intuitive</li>
                        <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Gestion multi-utilisateurs</li>
                        <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Sauvegarde automatique</li>
                    </ul>
                </div>
                
                <!-- Feature 5 -->
                <div class="feature-card bg-white dark:bg-gray-900 rounded-2xl p-8 shadow-lg hover:shadow-2xl animate-on-scroll" style="animation-delay: 0.8s;">
                    <div class="w-16 h-16 bg-gradient-to-br from-red-500 to-red-600 rounded-2xl flex items-center justify-center mb-6">
                        <i class="fas fa-mobile-alt text-white text-2xl"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-4 text-gray-900 dark:text-white">100% Mobile</h3>
                    <p class="text-gray-600 dark:text-gray-300 mb-6">
                        Interface optimisée pour tous les appareils. Vos clients et votre équipe peuvent tout faire depuis leur smartphone.
                    </p>
                    <ul class="space-y-2 text-sm text-gray-500 dark:text-gray-400">
                        <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Design responsive</li>
                        <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Application web</li>
                        <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Mode hors ligne</li>
                    </ul>
                </div>
                
                <!-- Feature 6 -->
                <div class="feature-card bg-white dark:bg-gray-900 rounded-2xl p-8 shadow-lg hover:shadow-2xl animate-on-scroll" style="animation-delay: 1s;">
                    <div class="w-16 h-16 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-2xl flex items-center justify-center mb-6">
                        <i class="fas fa-shield-alt text-white text-2xl"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-4 text-gray-900 dark:text-white">Sécurité Maximale</h3>
                    <p class="text-gray-600 dark:text-gray-300 mb-6">
                        Vos données sont protégées par les dernières technologies de sécurité. Conformité RGPD garantie.
                    </p>
                    <ul class="space-y-2 text-sm text-gray-500 dark:text-gray-400">
                        <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Chiffrement SSL</li>
                        <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Conformité RGPD</li>
                        <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Sauvegarde sécurisée</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Benefits Section -->
    <section id="benefits" class="py-20 bg-white dark:bg-gray-900">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16 animate-on-scroll">
                <h2 class="text-4xl sm:text-5xl font-display font-bold mb-6 gradient-text">
                    Pourquoi Choisir RestoQR ?
                </h2>
                <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                    Rejoignez des centaines de restaurants qui ont déjà transformé leur activité avec RestoQR
                </p>
            </div>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
                <!-- Benefits List -->
                <div class="space-y-8 animate-on-scroll">
                    <div class="flex items-start space-x-4">
                        <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center flex-shrink-0">
                            <i class="fas fa-rocket text-white"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold mb-2 text-gray-900 dark:text-white">Augmentez vos Revenus</h3>
                            <p class="text-gray-600 dark:text-gray-300">
                                Réduisez les temps d'attente et servez plus de clients. Nos utilisateurs voient une augmentation moyenne de 30% de leur chiffre d'affaires.
                            </p>
                        </div>
                    </div>
                    
                    <div class="flex items-start space-x-4">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center flex-shrink-0">
                            <i class="fas fa-users text-white"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold mb-2 text-gray-900 dark:text-white">Améliorez l'Expérience Client</h3>
                            <p class="text-gray-600 dark:text-gray-300">
                                Offrez une expérience moderne et sans contact. 95% de nos clients rapportent une amélioration de la satisfaction client.
                            </p>
                        </div>
                    </div>
                    
                    <div class="flex items-start space-x-4">
                        <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center flex-shrink-0">
                            <i class="fas fa-chart-line text-white"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold mb-2 text-gray-900 dark:text-white">Optimisez vos Opérations</h3>
                            <p class="text-gray-600 dark:text-gray-300">
                                Automatisez la prise de commande et réduisez les erreurs. Concentrez-vous sur ce qui compte vraiment : la cuisine !
                            </p>
                        </div>
                    </div>
                    
                    <div class="flex items-start space-x-4">
                        <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center flex-shrink-0">
                            <i class="fas fa-piggy-bank text-white"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold mb-2 text-gray-900 dark:text-white">Réduisez vos Coûts</h3>
                            <p class="text-gray-600 dark:text-gray-300">
                                Moins de personnel nécessaire pour la prise de commande. Économisez sur l'impression des menus et réduisez le gaspillage.
                            </p>
                        </div>
                    </div>
                </div>
                
                <!-- Visual Element -->
                <div class="relative animate-on-scroll" style="animation-delay: 0.3s;">
                    <div class="relative z-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-3xl p-8 text-white">
                        <div class="text-center mb-8">
                            <h3 class="text-3xl font-bold mb-4">Résultats Garantis</h3>
                            <p class="text-blue-100">Nos clients voient des résultats dès la première semaine</p>
                        </div>
                        
                        <div class="grid grid-cols-2 gap-6">
                            <div class="text-center">
                                <div class="text-4xl font-bold mb-2">+30%</div>
                                <div class="text-sm text-blue-100">Revenus</div>
                            </div>
                            <div class="text-center">
                                <div class="text-4xl font-bold mb-2">-50%</div>
                                <div class="text-sm text-blue-100">Temps d'attente</div>
                            </div>
                            <div class="text-center">
                                <div class="text-4xl font-bold mb-2">95%</div>
                                <div class="text-sm text-blue-100">Satisfaction</div>
                            </div>
                            <div class="text-center">
                                <div class="text-4xl font-bold mb-2">24/7</div>
                                <div class="text-sm text-blue-100">Support</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Decorative Elements -->
                    <div class="absolute -top-4 -left-4 w-24 h-24 bg-yellow-400 rounded-full opacity-20 animate-pulse"></div>
                    <div class="absolute -bottom-4 -right-4 w-32 h-32 bg-pink-400 rounded-full opacity-20 animate-pulse" style="animation-delay: 1s;"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-700 relative overflow-hidden">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-10">
            <div class="absolute inset-0" style="background-image: radial-gradient(circle at 25% 25%, white 2px, transparent 2px); background-size: 50px 50px;"></div>
        </div>
        
        <div class="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-white">
            <div class="animate-on-scroll">
                <h2 class="text-4xl sm:text-5xl font-display font-bold mb-6">
                    Prêt à Révolutionner Votre Restaurant ?
                </h2>
                <p class="text-xl sm:text-2xl mb-8 text-blue-100">
                    Rejoignez des centaines de restaurants qui ont déjà fait le choix de l'innovation
                </p>
                
                <div class="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6">
                    <a href="{% url 'restaurant:admin_login' %}" class="group inline-flex items-center px-8 py-4 bg-white text-gray-900 rounded-full font-semibold text-lg hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 hover:shadow-2xl">
                        <i class="fas fa-rocket mr-3 text-blue-600 group-hover:animate-bounce"></i>
                        Commencer Gratuitement
                        <i class="fas fa-arrow-right ml-3 group-hover:translate-x-1 transition-transform duration-300"></i>
                    </a>
                    
                    <a href="#contact" class="group inline-flex items-center px-8 py-4 border-2 border-white text-white rounded-full font-semibold text-lg hover:bg-white hover:text-gray-900 transition-all duration-300 transform hover:scale-105">
                        <i class="fas fa-phone mr-3 group-hover:animate-pulse"></i>
                        Nous Contacter
                    </a>
                </div>
                
                <div class="mt-8 text-sm text-blue-100">
                    <i class="fas fa-shield-alt mr-2"></i>
                    Aucun engagement • Configuration en 5 minutes • Support 24/7
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer id="contact" class="bg-gray-900 text-white py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- Company Info -->
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center space-x-3 mb-6">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                            <i class="fas fa-qrcode text-white text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold gradient-text">RestoQR</h3>
                            <p class="text-sm text-gray-400">L'avenir de la restauration</p>
                        </div>
                    </div>
                    <p class="text-gray-300 mb-6 max-w-md">
                        RestoQR révolutionne l'expérience restaurant avec des solutions QR code innovantes, une gestion intelligente et une interface moderne.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-blue-600 transition-colors duration-300">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-blue-400 transition-colors duration-300">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-pink-600 transition-colors duration-300">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-blue-700 transition-colors duration-300">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                    </div>
                </div>
                
                <!-- Quick Links -->
                <div>
                    <h4 class="text-lg font-semibold mb-6">Liens Rapides</h4>
                    <ul class="space-y-3">
                        <li><a href="#home" class="text-gray-300 hover:text-white transition-colors duration-300">Accueil</a></li>
                        <li><a href="#features" class="text-gray-300 hover:text-white transition-colors duration-300">Fonctionnalités</a></li>
                        <li><a href="#benefits" class="text-gray-300 hover:text-white transition-colors duration-300">Avantages</a></li>
                        <li><a href="{% url 'restaurant:admin_login' %}" class="text-gray-300 hover:text-white transition-colors duration-300">Connexion</a></li>
                    </ul>
                </div>
                
                <!-- Contact -->
                <div>
                    <h4 class="text-lg font-semibold mb-6">Contact</h4>
                    <ul class="space-y-3">
                        <li class="flex items-center">
                            <i class="fas fa-envelope mr-3 text-blue-500"></i>
                            <a href="mailto:<EMAIL>" class="text-gray-300 hover:text-white transition-colors duration-300"><EMAIL></a>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-phone mr-3 text-green-500"></i>
                            <a href="tel:+212620925113" class="text-gray-300 hover:text-white transition-colors duration-300">+212 620-925113</a>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-map-marker-alt mr-3 text-red-500"></i>
                            <span class="text-gray-300">Casablanca, Maroc</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-clock mr-3 text-yellow-500"></i>
                            <span class="text-gray-300">Support 24/7</span>
                        </li>
                    </ul>
                </div>
            </div>
            
            <div class="border-t border-gray-800 mt-12 pt-8 text-center">
                <p class="text-gray-400">
                    © 2025 RestoQR. Tous droits réservés. 
                    <span class="mx-2">•</span>
                    <a href="#" class="hover:text-white transition-colors duration-300">Politique de Confidentialité</a>
                    <span class="mx-2">•</span>
                    <a href="#" class="hover:text-white transition-colors duration-300">Conditions d'Utilisation</a>
                </p>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button id="back-to-top" class="fixed bottom-8 right-8 w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 opacity-0 pointer-events-none z-50">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- JavaScript -->
    <script>
        // Dark Mode Toggle
        function toggleDarkMode() {
            document.documentElement.classList.toggle('dark');
            localStorage.setItem('darkMode', document.documentElement.classList.contains('dark'));
        }

        // Initialize Dark Mode
        if (localStorage.getItem('darkMode') === 'true' || (!localStorage.getItem('darkMode') && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            document.documentElement.classList.add('dark');
        }

        // Mobile Menu Toggle
        document.getElementById('mobile-menu-btn').addEventListener('click', function() {
            document.getElementById('mobile-menu').classList.toggle('hidden');
        });

        // Smooth Scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Scroll to Demo Function
        function scrollToDemo() {
            document.getElementById('features').scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }

        // Animate on Scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.animate-on-scroll').forEach(el => {
            observer.observe(el);
        });

        // Counter Animation
        function animateCounters() {
            const counters = document.querySelectorAll('[data-count]');
            counters.forEach(counter => {
                const target = parseInt(counter.getAttribute('data-count'));
                let current = 0;
                const increment = target / 100;
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        counter.textContent = target;
                        clearInterval(timer);
                    } else {
                        counter.textContent = Math.floor(current);
                    }
                }, 20);
            });
        }

        // Start counter animation when hero section is visible
        const heroObserver = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateCounters();
                    heroObserver.unobserve(entry.target);
                }
            });
        }, { threshold: 0.5 });

        heroObserver.observe(document.getElementById('home'));

        // Back to Top Button
        const backToTopBtn = document.getElementById('back-to-top');
        
        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 300) {
                backToTopBtn.classList.remove('opacity-0', 'pointer-events-none');
                backToTopBtn.classList.add('opacity-100');
            } else {
                backToTopBtn.classList.add('opacity-0', 'pointer-events-none');
                backToTopBtn.classList.remove('opacity-100');
            }
        });

        backToTopBtn.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        // Navbar Background on Scroll
        const navbar = document.querySelector('nav');
        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 50) {
                navbar.classList.add('backdrop-blur-xl');
            } else {
                navbar.classList.remove('backdrop-blur-xl');
            }
        });

        // Preloader (optional)
        window.addEventListener('load', function() {
            document.body.classList.add('loaded');
        });

        // Add loading states to CTA buttons
        document.querySelectorAll('a[href*="admin"]').forEach(link => {
            link.addEventListener('click', function() {
                const icon = this.querySelector('i.fa-rocket');
                if (icon) {
                    icon.classList.remove('fa-rocket');
                    icon.classList.add('fa-spinner', 'animate-spin');
                }
            });
        });
    </script>
</body>
</html>