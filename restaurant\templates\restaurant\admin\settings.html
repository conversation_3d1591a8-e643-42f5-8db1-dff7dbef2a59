{% extends 'restaurant/admin/base.html' %}

{% block title %}Settings - RestoQR Admin{% endblock %}
{% block page_title %}Settings{% endblock %}

{% block admin_content %}
<div class="max-w-4xl mx-auto space-y-6 fade-in">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <h1 class="text-2xl font-bold text-gray-900 dark:text-light_text">Restaurant Settings</h1>
    </div>

    <!-- Settings Form -->
    <div class="bg-white dark:bg-dark_card rounded-xl shadow-sm overflow-hidden">
        <form method="post" enctype="multipart/form-data" class="p-6 space-y-6">
            {% csrf_token %}
            <div class="space-y-4">
                {% for field in form %}
                    <div>
                        <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-light_text mb-1">
                            {{ field.label }}
                        </label>
                        {{ field }}
                        {% if field.help_text %}
                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">{{ field.help_text }}</p>
                        {% endif %}
                        {% if field.errors %}
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ field.errors.0 }}</p>
                        {% endif %}
                    </div>
                {% endfor %}
            </div>
            
            <div class="flex justify-end pt-6 border-t border-gray-200 dark:border-gray-700">
                <button type="submit" class="px-6 py-2 bg-primary text-white rounded-lg hover:bg-secondary transition-colors duration-200">
                    <i class="fas fa-save mr-2"></i>
                    Save Settings
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

