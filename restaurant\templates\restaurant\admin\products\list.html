{% extends 'restaurant/admin/base.html' %}

{% block title %}Products - RestoQR Admin{% endblock %}
{% block page_title %}Products{% endblock %}

{% block admin_content %}
<div class="space-y-6 fade-in">
    <!-- Header with Search and Add Button -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div class="flex items-center space-x-4 mb-4 sm:mb-0">
            <h1 class="text-2xl font-bold text-gray-900 dark:text-light_text">Products</h1>
            <span class="bg-primary text-white px-3 py-1 rounded-full text-sm font-medium">
                {{ page_obj.paginator.count }} total
            </span>
        </div>
        
        <a href="{% url 'restaurant:product_add' %}" class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-secondary transition-colors duration-200 hover-scale">
            <i class="fas fa-plus mr-2"></i>
            Add Product
        </a>
    </div>
    
    <!-- Filters and Search -->
    <div class="bg-white dark:bg-dark_card rounded-xl shadow-sm p-6">
        <form method="get" class="flex flex-col md:flex-row md:items-end space-y-4 md:space-y-0 md:space-x-4">
            <div class="flex-1">
                <label for="search" class="block text-sm font-medium text-gray-700 dark:text-light_text mb-1">Search Products</label>
                <div class="relative">
                    <input type="text" id="search" name="search" value="{{ search_query }}" 
                           placeholder="Search by name or description..."
                           class="w-full px-4 py-2 pl-10 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-gray-900 dark:text-light_text">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                </div>
            </div>
            
            <div class="md:w-48">
                <label for="category" class="block text-sm font-medium text-gray-700 dark:text-light_text mb-1">Category</label>
                <select id="category" name="category" class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-gray-900 dark:text-light_text">
                    <option value="">All Categories</option>
                    {% for category in categories %}
                        <option value="{{ category.id }}" {% if selected_category == category.id|stringformat:"s" %}selected{% endif %}>
                            {{ category.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            
            <button type="submit" class="px-6 py-2 bg-primary text-white rounded-lg hover:bg-secondary transition-colors duration-200">
                <i class="fas fa-filter mr-2"></i>
                Filter
            </button>
            
            {% if search_query or selected_category %}
                <a href="{% url 'restaurant:product_list' %}" class="px-6 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors duration-200">
                    <i class="fas fa-times mr-2"></i>
                    Clear
                </a>
            {% endif %}
        </form>
    </div>
    
    <!-- Products Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {% for product in page_obj %}
         <div class="bg-white dark:bg-dark_card rounded-xl shadow-sm overflow-hidden hover-scale">
            <!-- Product Image -->
            <div class="h-48 bg-gray-200 dark:bg-gray-700 relative">
                {% if product.image %}
                    <img src="{{ product.image.url }}" alt="{{ product.name }}" class="w-full h-full object-cover">
                {% else %}
                    <div class="w-full h-full flex items-center justify-center">
                        <i class="fas fa-image text-gray-400 text-4xl"></i>
                    </div>
                {% endif %}
                
                <!-- Availability Badge -->
                <div class="absolute top-2 right-2">
                    {% if product.is_available %}
                        <span class="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium">Available</span>
                    {% else %}
                        <span class="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-medium">Unavailable</span>
                    {% endif %}
                </div>
            </div>
            
            <!-- Product Info -->
            <div class="p-4">
                <div class="flex items-start justify-between mb-2">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-light_text truncate">{{ product.name }}</h3>
                    <span class="text-lg font-bold text-primary ml-2">{{ product.price }} dh</span>
                </div>
                
                <p class="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">{{ product.description }}</p>
                
                <div class="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-4">
                    <span class="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">{{ product.category.name }}</span>
                    <span><i class="fas fa-clock mr-1"></i>{{ product.preparation_time }}min</span>
                </div>
                
                <!-- Action Buttons -->
                <div class="flex space-x-2">
                    <a href="{% url 'restaurant:product_edit' product.pk %}" 
                       class="flex-1 bg-blue-500 text-white text-center py-2 rounded-lg hover:bg-blue-600 transition-colors duration-200">
                        <i class="fas fa-edit mr-1"></i>
                        Edit
                    </a>
                    <button onclick="deleteProduct({{ product.pk }}, '{{ product.name }}')" 
                            class="flex-1 bg-red-500 text-white py-2 rounded-lg hover:bg-red-600 transition-colors duration-200">
                        <i class="fas fa-trash mr-1"></i>
                        Delete
                    </button>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-span-full text-center py-12">
            <i class="fas fa-hamburger text-gray-400 text-6xl mb-4"></i>
            <h3 class="text-xl font-medium text-gray-900 dark:text-light_text mb-2">No products found</h3>
            <p class="text-gray-500 dark:text-gray-400 mb-4">
                {% if search_query or selected_category %}
                    Try adjusting your search criteria or filters.
                {% else %}
                    Get started by adding your first product.
                {% endif %}
            </p>
            <a href="{% url 'restaurant:product_add' %}" class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-secondary transition-colors duration-200">
                <i class="fas fa-plus mr-2"></i>
                Add Product
            </a>
        </div>
        {% endfor %}
    </div>
    
    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <div class="flex items-center justify-between bg-white dark:bg-dark_card px-6 py-3 rounded-xl shadow-sm">
        <div class="flex items-center text-sm text-gray-700 dark:text-light_text">
            <span>Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} results</span>
        </div>
        
        <div class="flex items-center space-x-2">
            {% if page_obj.has_previous %}
                <a href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_category %}&category={{ selected_category }}{% endif %}" 
                   class="px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-light_text rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200">
                    <i class="fas fa-chevron-left"></i>
                </a>
            {% endif %}
            
            {% for num in page_obj.paginator.page_range %}
                {% if page_obj.number == num %}
                    <span class="px-3 py-2 text-sm bg-primary text-white rounded-lg">{{ num }}</span>
                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <a href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_category %}&category={{ selected_category }}{% endif %}" 
                       class="px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-light_text rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200">
                        {{ num }}
                    </a>
                {% endif %}
            {% endfor %}
            
            {% if page_obj.has_next %}
                <a href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_category %}&category={{ selected_category }}{% endif %}" 
                   class="px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-light_text rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200">
                    <i class="fas fa-chevron-right"></i>
                </a>
            {% endif %}
        </div>
    </div>
    {% endif %}
</div>

<!-- Hidden CSRF Token -->
<form style="display: none;">
    {% csrf_token %}
</form>

<script>
    function deleteProduct(productId, productName) {
        Swal.fire({
            title: 'Supprimer le Produit ?',
            html: `
                <div class="text-center">
                    <div class="text-6xl mb-4">🗑️</div>
                    <p class="text-lg mb-2">Êtes-vous sûr de vouloir supprimer</p>
                    <p class="font-bold text-red-600">"${productName}"</p>
                    <p class="text-sm text-gray-500 mt-2">Cette action est irréversible</p>
                </div>
            `,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#EF4444',
            cancelButtonColor: '#6B7280',
            confirmButtonText: '<i class="fas fa-trash mr-2"></i>Oui, supprimer !',
            cancelButtonText: '<i class="fas fa-times mr-2"></i>Annuler',
            reverseButtons: true,
            focusCancel: true,
            customClass: {
                popup: 'rounded-2xl',
                confirmButton: 'rounded-lg px-6 py-3 font-semibold',
                cancelButton: 'rounded-lg px-6 py-3 font-semibold'
            }
        }).then((result) => {
            if (result.isConfirmed) {
                // Show loading state
                Swal.fire({
                    title: 'Suppression en cours...',
                    html: '<div class="text-center"><i class="fas fa-spinner fa-spin text-4xl text-blue-500 mb-4"></i><p>Veuillez patienter</p></div>',
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    showConfirmButton: false,
                    customClass: {
                        popup: 'rounded-2xl'
                    }
                });
                
                // Create a form and submit it
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `{% url 'restaurant:product_delete' 0 %}`.replace('0', productId);
                
                // Get CSRF token
                const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
                const csrfInput = document.createElement('input');
                csrfInput.type = 'hidden';
                csrfInput.name = 'csrfmiddlewaretoken';
                csrfInput.value = csrfToken;
                form.appendChild(csrfInput);
                
                document.body.appendChild(form);
                form.submit();
            }
        });
    }
    
    // Add loading states to buttons
    document.addEventListener('DOMContentLoaded', function() {
        // Add hover effects to action buttons
        const editButtons = document.querySelectorAll('a[href*="edit"]');
        const deleteButtons = document.querySelectorAll('button[onclick*="deleteProduct"]');
        
        editButtons.forEach(button => {
            button.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
                this.style.boxShadow = '0 4px 12px rgba(59, 130, 246, 0.3)';
            });
            
            button.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = 'none';
            });
        });
        
        deleteButtons.forEach(button => {
            button.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
                this.style.boxShadow = '0 4px 12px rgba(239, 68, 68, 0.3)';
            });
            
            button.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = 'none';
            });
        });
        
        // Add click animation
        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
            });
        });
    });
</script>
{% endblock %}

