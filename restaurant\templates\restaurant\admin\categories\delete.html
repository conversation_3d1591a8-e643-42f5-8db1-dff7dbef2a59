{% extends 'restaurant/admin/base.html' %}

{% block title %}Delete Category - RestoQR Admin{% endblock %}
{% block page_title %}Delete Category{% endblock %}

{% block admin_content %}
<div class="space-y-6 fade-in">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <a href="{% url 'restaurant:category_list' %}" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                <i class="fas fa-arrow-left text-xl"></i>
            </a>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-light_text">
                Delete Category: {{ category.name }}
            </h1>
        </div>
    </div>
    
    <!-- Delete Confirmation -->
    <div class="bg-white dark:bg-dark_card rounded-xl shadow-sm p-6">
        <p class="text-gray-700 dark:text-gray-300 mb-4">
            Are you sure you want to delete the category "<span class="font-semibold">{{ category.name }}</span>"?
            This action cannot be undone.
        </p>
        
        <form method="post">
            {% csrf_token %}
            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200 dark:border-gray-700">
                <a href="{% url 'restaurant:category_list' %}" class="btn-secondary">
                    Cancel
                </a>
                <button type="submit" class="btn-danger">
                    <i class="fas fa-trash mr-2"></i>
                    Delete Category
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

