{% extends 'restaurant/admin/base.html' %}

{% block title %}{% if form.instance.pk %}Edit Category{% else %}Add Category{% endif %} - RestoQR Admin{% endblock %}
{% block page_title %}{% if form.instance.pk %}Edit Category{% else %}Add Category{% endif %}{% endblock %}

{% block admin_content %}
<div class="space-y-6 fade-in">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <a href="{% url 'restaurant:category_list' %}" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                <i class="fas fa-arrow-left text-xl"></i>
            </a>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-light_text">
                {% if form.instance.pk %}Edit Category{% else %}Add New Category{% endif %}
            </h1>
        </div>
    </div>
    
    <!-- Form -->
    <div class="bg-white dark:bg-dark_card rounded-xl shadow-sm p-6">
        <form method="post" class="space-y-6">
            {% csrf_token %}
            
            <!-- Category Name -->
            <div>
                <label for="{{ form.name.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-light_text mb-2">
                    Category Name *
                </label>
                {{ form.name }}
                {% if form.name.errors %}
                    <div class="mt-1 text-sm text-red-600">
                        {{ form.name.errors.0 }}
                    </div>
                {% endif %}
            </div>
            
            <!-- Description -->
            <div>
                <label for="{{ form.description.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Description
                </label>
                {{ form.description }}
                {% if form.description.errors %}
                    <div class="mt-1 text-sm text-red-600">
                        {{ form.description.errors.0 }}
                    </div>
                {% endif %}
            </div>
            
            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200 dark:border-gray-700">
                <a href="{% url 'restaurant:category_list' %}" class="btn-secondary">
                    Cancel
                </a>
                <button type="submit" class="btn-primary">
                    <i class="fas fa-save mr-2"></i>
                    {% if form.instance.pk %}Update Category{% else %}Create Category{% endif %}
                </button>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus on the first form field
    const firstField = document.querySelector('input[type="text"]');
    if (firstField) {
        firstField.focus();
    }
});
</script>
{% endblock %}

