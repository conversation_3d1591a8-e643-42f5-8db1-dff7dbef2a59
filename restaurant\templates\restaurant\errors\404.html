{% extends 'restaurant/base.html' %}

{% block title %}Page Not Found - RestoQR{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 dark:bg-dark_background flex items-center justify-center px-4">
    <div class="max-w-md w-full text-center">
        <!-- Error <PERSON>tration -->
        <div class="mb-8">
            <div class="mx-auto w-32 h-32 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mb-6">
                <i class="fas fa-exclamation-triangle text-red-600 dark:text-red-400 text-5xl"></i>
            </div>
            
            <h1 class="text-6xl font-bold text-gray-900 dark:text-light_text mb-4">404</h1>
            <h2 class="text-2xl font-semibold text-gray-700 dark:text-gray-300 mb-4">Page Not Found</h2>
            <p class="text-gray-600 dark:text-gray-400 mb-8">
                Sorry, the page you are looking for doesn't exist or has been moved.
            </p>
        </div>
        
        <!-- Action Buttons -->
        <div class="space-y-4">
            <a href="{% url 'restaurant:admin_dashboard' %}" 
               class="inline-flex items-center px-6 py-3 bg-primary text-white rounded-lg hover:bg-secondary transition-colors duration-200 font-medium">
                <i class="fas fa-home mr-2"></i>
                Go to Dashboard
            </a>
            
            <div class="flex justify-center space-x-4">
                <button onclick="history.back()" 
                        class="inline-flex items-center px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Go Back
                </button>
                
                <a href="{% url 'restaurant:product_list' %}" 
                   class="inline-flex items-center px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200">
                    <i class="fas fa-hamburger mr-2"></i>
                    View Menu
                </a>
            </div>
        </div>
        
        <!-- Help Section -->
        <div class="mt-12 p-6 bg-white dark:bg-dark_card rounded-xl shadow-sm">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-light_text mb-3">Need Help?</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                If you believe this is an error, please contact our support team.
            </p>
            <div class="flex justify-center space-x-4 text-sm">
                <a href="mailto:<EMAIL>" class="text-primary hover:text-secondary">
                    <i class="fas fa-envelope mr-1"></i>
                    Email Support
                </a>
                <span class="text-gray-400">|</span>
                <a href="tel:+1234567890" class="text-primary hover:text-secondary">
                    <i class="fas fa-phone mr-1"></i>
                    Call Support
                </a>
            </div>
        </div>
    </div>
</div>

<style>
    .error-animation {
        animation: bounce 2s infinite;
    }
    
    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% {
            transform: translateY(0);
        }
        40% {
            transform: translateY(-10px);
        }
        60% {
            transform: translateY(-5px);
        }
    }
</style>

<script>
    // Add some interactive elements
    document.addEventListener('DOMContentLoaded', function() {
        // Add error animation to the icon
        const errorIcon = document.querySelector('.fas.fa-exclamation-triangle');
        if (errorIcon) {
            errorIcon.parentElement.classList.add('error-animation');
        }
        
        // Track 404 errors (you can send this to analytics)
        console.log('404 Error:', window.location.pathname);
    });
</script>
{% endblock %}