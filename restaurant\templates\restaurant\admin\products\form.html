{% extends 'restaurant/admin/base.html' %}

{% block title %}{{ title }} - RestoQR Admin{% endblock %}
{% block page_title %}{{ title }}{% endblock %}

{% block admin_content %}
<div class="max-w-4xl mx-auto space-y-6 fade-in">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <a href="{% url 'restaurant:product_list' %}" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                <i class="fas fa-arrow-left text-xl"></i>
            </a>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-light_text">{{ title }}</h1>
        </div>
    </div>
    
    <!-- Form -->
    <div class="bg-white dark:bg-dark_card rounded-xl shadow-sm overflow-hidden">
        <form method="post" enctype="multipart/form-data" class="p-6 space-y-6">
            {% csrf_token %}
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Left Column -->
                <div class="space-y-6">
                    <!-- Basic Information -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-light_text mb-4">Basic Information</h3>
                        
                        <div class="space-y-4">
                            <div>
                                <label for="{{ form.name.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-light_text mb-1">
                                    Product Name *
                                </label>
                                {{ form.name }}
                                {% if form.name.errors %}
                                    <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.name.errors.0 }}</p>
                                {% endif %}
                            </div>
                            
                            <div>
                                <label for="{{ form.description.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Description *
                                </label>
                                {{ form.description }}
                                {% if form.description.errors %}
                                    <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.description.errors.0 }}</p>
                                {% endif %}
                            </div>
                            
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label for="{{ form.price.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        Price ($) *
                                    </label>
                                    {{ form.price }}
                                    {% if form.price.errors %}
                                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.price.errors.0 }}</p>
                                    {% endif %}
                                </div>
                                
                                <div>
                                    <label for="{{ form.preparation_time.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        Prep Time (min) *
                                    </label>
                                    {{ form.preparation_time }}
                                    {% if form.preparation_time.errors %}
                                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.preparation_time.errors.0 }}</p>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div>
                                <label for="{{ form.category.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Category *
                                </label>
                                {{ form.category }}
                                {% if form.category.errors %}
                                    <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.category.errors.0 }}</p>
                                {% endif %}
                            </div>
                            
                            <div class="flex items-center">
                                {{ form.is_available }}
                                <label for="{{ form.is_available.id_for_label }}" class="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                                    Available for ordering
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Right Column -->
                <div class="space-y-6">
                    <!-- Product Image -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-light_text mb-4">Product Image</h3>
                        
                        <div class="space-y-4">
                            <!-- Current Image Preview -->
                            {% if product and product.image %}
                            <div id="current-image" class="relative">
                                <img src="{{ product.image.url }}" alt="{{ product.name }}" class="w-full h-48 object-cover rounded-lg">
                                <div class="absolute top-2 right-2">
                                    <button type="button" onclick="removeCurrentImage()" class="bg-red-500 text-white p-2 rounded-full hover:bg-red-600 transition-colors duration-200">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                            {% endif %}
                            
                            <!-- Image Upload -->
                            <div class="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center">
                                <div id="image-preview" class="hidden">
                                    <img id="preview-img" class="w-full h-48 object-cover rounded-lg mb-4">
                                </div>
                                
                                <div id="upload-placeholder" class="{% if product and product.image %}hidden{% endif %}">
                                    <i class="fas fa-cloud-upload-alt text-gray-400 text-4xl mb-4"></i>
                                    <p class="text-gray-600 dark:text-gray-400 mb-2">Click to upload or drag and drop</p>
                                    <p class="text-sm text-gray-500 dark:text-gray-500">PNG, JPG, GIF up to 10MB</p>
                                </div>
                                
                                {{ form.image }}
                                {% if form.image.errors %}
                                    <p class="mt-2 text-sm text-red-600 dark:text-red-400">{{ form.image.errors.0 }}</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <!-- Preview Card -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-light_text mb-4">Preview</h3>
                        <div id="product-preview" class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border">
                            <div class="h-32 bg-gray-200 dark:bg-gray-600 rounded-lg mb-3 flex items-center justify-center">
                                <i class="fas fa-image text-gray-400 text-2xl"></i>
                            </div>
                            <h4 class="font-semibold text-gray-900 dark:text-light_text mb-1">Product Name</h4>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">Product description...</p>
                            <div class="flex items-center justify-between">
                                <span class="text-lg font-bold text-primary">$0.00</span>
                                <span class="text-sm text-gray-500"><i class="fas fa-clock mr-1"></i>0min</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200 dark:border-gray-700">
                <a href="{% url 'restaurant:product_list' %}" class="px-6 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-light_text rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                    Cancel
                </a>
                <button type="submit" class="px-6 py-2 bg-primary text-white rounded-lg hover:bg-secondary transition-colors duration-200">
                    <i class="fas fa-save mr-2"></i>
                    {% if product %}Update Product{% else %}Create Product{% endif %}
                </button>
            </div>
        </form>
    </div>
</div>

<script>
    // Image preview functionality
    const imageInput = document.getElementById('{{ form.image.id_for_label }}');
    const imagePreview = document.getElementById('image-preview');
    const previewImg = document.getElementById('preview-img');
    const uploadPlaceholder = document.getElementById('upload-placeholder');
    
    imageInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImg.src = e.target.result;
                imagePreview.classList.remove('hidden');
                uploadPlaceholder.classList.add('hidden');
                updatePreview();
            };
            reader.readAsDataURL(file);
        }
    });
    
    // Live preview updates
    function updatePreview() {
        const preview = document.getElementById('product-preview');
        const nameInput = document.getElementById('{{ form.name.id_for_label }}');
        const descInput = document.getElementById('{{ form.description.id_for_label }}');
        const priceInput = document.getElementById('{{ form.price.id_for_label }}');
        const timeInput = document.getElementById('{{ form.preparation_time.id_for_label }}');
        
        const nameEl = preview.querySelector('h4');
        const descEl = preview.querySelector('p');
        const priceEl = preview.querySelector('.text-primary');
        const timeEl = preview.querySelector('.text-gray-500');
        
        nameEl.textContent = nameInput.value || 'Product Name';
        descEl.textContent = descInput.value || 'Product description...';
        priceEl.textContent = '$' + (priceInput.value || '0.00');
        timeEl.innerHTML = '<i class="fas fa-clock mr-1"></i>' + (timeInput.value || '0') + 'min';
    }
    
    // Add event listeners for live preview
    ['{{ form.name.id_for_label }}', '{{ form.description.id_for_label }}', '{{ form.price.id_for_label }}', '{{ form.preparation_time.id_for_label }}'].forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('input', updatePreview);
        }
    });
    
    // Initialize preview
    updatePreview();
    
    // Remove current image function
    function removeCurrentImage() {
        document.getElementById('current-image').style.display = 'none';
        uploadPlaceholder.classList.remove('hidden');
    }
</script>
{% endblock %}

