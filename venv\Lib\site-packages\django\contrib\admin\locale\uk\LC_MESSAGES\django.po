# This file is distributed under the same license as the Django package.
#
# Translators:
# Abb<PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON> <o.chern<PERSON><EMAIL>>, 2014
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2015
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2016
# <PERSON>, 2014,2017
# <AUTHOR> <EMAIL>, 2021-2023,2025
# <PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2015
# <PERSON> <<EMAIL>>, 2014
# <PERSON> <<EMAIL>>, 2015
# Myko<PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2014
# <PERSON><PERSON><PERSON> <s.kuzmen<PERSON>@gmail.com>, 2011
# taras<PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON>, 2016
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-19 11:30-0500\n"
"PO-Revision-Date: 2025-04-01 15:04-0500\n"
"Last-Translator: Illia Volochii <<EMAIL>>, 2021-2023,2025\n"
"Language-Team: Ukrainian (http://app.transifex.com/django/django/language/"
"uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != "
"11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % "
"100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || "
"(n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#, python-format
msgid "Delete selected %(verbose_name_plural)s"
msgstr "Видалити обрані %(verbose_name_plural)s"

#, python-format
msgid "Successfully deleted %(count)d %(items)s."
msgstr "Успішно видалено %(count)d %(items)s."

#, python-format
msgid "Cannot delete %(name)s"
msgstr "Не вдається видалити %(name)s"

msgid "Delete multiple objects"
msgstr "Видалити кілька об'єктів"

msgid "Administration"
msgstr "Адміністрування"

msgid "All"
msgstr "Всі"

msgid "Yes"
msgstr "Так"

msgid "No"
msgstr "Ні"

msgid "Unknown"
msgstr "Невідомо"

msgid "Any date"
msgstr "Будь-яка дата"

msgid "Today"
msgstr "Сьогодні"

msgid "Past 7 days"
msgstr "Останні 7 днів"

msgid "This month"
msgstr "Цього місяця"

msgid "This year"
msgstr "Цього року"

msgid "No date"
msgstr "Без дати"

msgid "Has date"
msgstr "Має дату"

msgid "Empty"
msgstr "Порожні"

msgid "Not empty"
msgstr "Непорожні"

#, python-format
msgid ""
"Please enter the correct %(username)s and password for a staff account. Note "
"that both fields may be case-sensitive."
msgstr ""
"Будь ласка, введіть правильні %(username)s і пароль для облікового запису "
"персоналу. Зауважте, що обидва поля можуть бути чутливі до регістру."

msgid "Action:"
msgstr "Дія:"

#, python-format
msgid "Add another %(verbose_name)s"
msgstr "Додати ще %(verbose_name)s"

msgid "Remove"
msgstr "Видалити"

msgid "Addition"
msgstr "Додавання"

msgid "Change"
msgstr "Змінити"

msgid "Deletion"
msgstr "Видалення"

msgid "action time"
msgstr "час дії"

msgid "user"
msgstr "користувач"

msgid "content type"
msgstr "тип вмісту"

msgid "object id"
msgstr "id об'єкта"

#. Translators: 'repr' means representation
#. (https://docs.python.org/library/functions.html#repr)
msgid "object repr"
msgstr "представлення об'єкта (repr)"

msgid "action flag"
msgstr "позначка дії"

msgid "change message"
msgstr "змінити повідомлення"

msgid "log entry"
msgstr "запис у журналі"

msgid "log entries"
msgstr "записи в журналі"

#, python-format
msgid "Added “%(object)s”."
msgstr "Додано \"%(object)s\"."

#, python-format
msgid "Changed “%(object)s” — %(changes)s"
msgstr "Змінено \"%(object)s\" - %(changes)s"

#, python-format
msgid "Deleted “%(object)s.”"
msgstr "Видалено \"%(object)s.\""

msgid "LogEntry Object"
msgstr "Об'єкт журнального запису"

#, python-brace-format
msgid "Added {name} “{object}”."
msgstr "Додано {name} \"{object}\"."

msgid "Added."
msgstr "Додано."

msgid "and"
msgstr "та"

#, python-brace-format
msgid "Changed {fields} for {name} “{object}”."
msgstr "Змінені {fields} для {name} \"{object}\"."

#, python-brace-format
msgid "Changed {fields}."
msgstr "Змінені {fields}."

#, python-brace-format
msgid "Deleted {name} “{object}”."
msgstr "Видалено {name} \"{object}\"."

msgid "No fields changed."
msgstr "Поля не змінені."

msgid "None"
msgstr "Ніщо"

msgid "Hold down “Control”, or “Command” on a Mac, to select more than one."
msgstr ""
"Натисність \"Control\" або \"Command\" на Mac-пристрої, щоб вибрати більше "
"аніж один."

msgid "Select this object for an action - {}"
msgstr "Вибрати цей об'єкт для дії – {}"

#, python-brace-format
msgid "The {name} “{obj}” was added successfully."
msgstr "{name} \"{obj}\" було додано успішно."

msgid "You may edit it again below."
msgstr "Ви можете відредагувати це знову."

#, python-brace-format
msgid ""
"The {name} “{obj}” was added successfully. You may add another {name} below."
msgstr "{name} \"{obj}\" було змінено успішно. Ви можете додати інше {name}."

#, python-brace-format
msgid ""
"The {name} “{obj}” was changed successfully. You may edit it again below."
msgstr ""
"{name} \"{obj}\" було змінено успішно. Нижче Ви можете редагувати його знову."

#, python-brace-format
msgid ""
"The {name} “{obj}” was changed successfully. You may add another {name} "
"below."
msgstr "{name} \"{obj}\" було змінено успішно. Ви можете додати інше {name}."

#, python-brace-format
msgid "The {name} “{obj}” was changed successfully."
msgstr "{name} \"{obj}\" було змінено успішно."

msgid ""
"Items must be selected in order to perform actions on them. No items have "
"been changed."
msgstr ""
"Для виконання дії необхідно обрати елемент. Жодний елемент не був змінений."

msgid "No action selected."
msgstr "Дія не обрана."

#, python-format
msgid "The %(name)s “%(obj)s” was deleted successfully."
msgstr "%(name)s \"%(obj)s\" був видалений успішно."

#, python-format
msgid "%(name)s with ID “%(key)s” doesn’t exist. Perhaps it was deleted?"
msgstr "%(name)s з ID \"%(key)s\" не існує. Можливо, воно було видалене?"

#, python-format
msgid "Add %s"
msgstr "Додати %s"

#, python-format
msgid "Change %s"
msgstr "Змінити %s"

#, python-format
msgid "View %s"
msgstr "Переглянути %s"

msgid "Database error"
msgstr "Помилка бази даних"

#, python-format
msgid "%(count)s %(name)s was changed successfully."
msgid_plural "%(count)s %(name)s were changed successfully."
msgstr[0] "%(count)s %(name)s був успішно змінений."
msgstr[1] "%(count)s %(name)s були успішно змінені."
msgstr[2] "%(count)s %(name)s було успішно змінено."
msgstr[3] "%(count)s %(name)s було успішно змінено."

#, python-format
msgid "%(total_count)s selected"
msgid_plural "All %(total_count)s selected"
msgstr[0] "%(total_count)s обраний"
msgstr[1] "%(total_count)s обрані"
msgstr[2] "Усі %(total_count)s обрано"
msgstr[3] "Усі %(total_count)s обрано"

#, python-format
msgid "0 of %(cnt)s selected"
msgstr "0 з %(cnt)s обрано"

msgid "Delete"
msgstr "Видалити"

#, python-format
msgid "Change history: %s"
msgstr "Історія змін: %s"

#. Translators: Model verbose name and instance
#. representation, suitable to be an item in a
#. list.
#, python-format
msgid "%(class_name)s %(instance)s"
msgstr "%(class_name)s %(instance)s"

#, python-format
msgid ""
"Deleting %(class_name)s %(instance)s would require deleting the following "
"protected related objects: %(related_objects)s"
msgstr ""
"Видалення %(class_name)s %(instance)s вимагатиме видалення наступних "
"захищених пов'язаних об'єктів: %(related_objects)s"

msgid "Django site admin"
msgstr "Django сайт адміністрування"

msgid "Django administration"
msgstr "Django адміністрування"

msgid "Site administration"
msgstr "Адміністрування сайта"

msgid "Log in"
msgstr "Увійти"

#, python-format
msgid "%(app)s administration"
msgstr "Адміністрування %(app)s"

msgid "Page not found"
msgstr "Сторінка не знайдена"

msgid "We’re sorry, but the requested page could not be found."
msgstr "На жаль, запрошену сторінку не знайдено."

msgid "Home"
msgstr "Домівка"

msgid "Server error"
msgstr "Помилка сервера"

msgid "Server error (500)"
msgstr "Помилка сервера (500)"

msgid "Server Error <em>(500)</em>"
msgstr "Помилка сервера <em>(500)</em>"

msgid ""
"There’s been an error. It’s been reported to the site administrators via "
"email and should be fixed shortly. Thanks for your patience."
msgstr ""
"Сталася помилка. Вона була відправлена адміністраторам сайту через email і "
"має бути вирішена швидко. Дякуємо за ваше терпіння."

msgid "Run the selected action"
msgstr "Виконати обрану дію"

msgid "Go"
msgstr "Вперед"

msgid "Click here to select the objects across all pages"
msgstr "Натисніть тут, щоб вибрати об'єкти на всіх сторінках"

#, python-format
msgid "Select all %(total_count)s %(module_name)s"
msgstr "Обрати всі %(total_count)s %(module_name)s"

msgid "Clear selection"
msgstr "Скинути вибір"

msgid "Breadcrumbs"
msgstr "Навігаційний рядок"

#, python-format
msgid "Models in the %(name)s application"
msgstr "Моделі у %(name)s додатку"

msgid "Model name"
msgstr ""

msgid "Add link"
msgstr ""

msgid "Change or view list link"
msgstr ""

msgid "Add"
msgstr "Додати"

msgid "View"
msgstr "Переглянути"

msgid "You don’t have permission to view or edit anything."
msgstr "Ви не маєте дозволу переглядати чи редагувати будь-чого."

msgid "After you’ve created a user, you’ll be able to edit more user options."
msgstr ""

msgid "Error:"
msgstr "Помилка:"

msgid "Change password"
msgstr "Змінити пароль"

msgid "Set password"
msgstr "Встановити пароль"

msgid "Please correct the error below."
msgid_plural "Please correct the errors below."
msgstr[0] "Будь ласка, виправте наведену нижче помилку."
msgstr[1] "Будь ласка, виправте наведені нижче помилки."
msgstr[2] "Будь ласка, виправте наведені нижче помилки."
msgstr[3] "Будь ласка, виправте наведені нижче помилки."

#, python-format
msgid "Enter a new password for the user <strong>%(username)s</strong>."
msgstr "Введіть новий пароль для користувача <strong>%(username)s</strong>."

msgid ""
"This action will <strong>enable</strong> password-based authentication for "
"this user."
msgstr ""
"Ця дія <strong>увімкне</strong> автентифікацію на основі пароля для "
"користувача."

msgid "Disable password-based authentication"
msgstr "Вимкнути автентифікацію на основі паролю"

msgid "Enable password-based authentication"
msgstr "Увімкнути автентифікацію на основі паролю"

msgid "Skip to main content"
msgstr "Перейти до основного вмісту"

msgid "Welcome,"
msgstr "Вітаємо,"

msgid "View site"
msgstr "Дивитися сайт"

msgid "Documentation"
msgstr "Документація"

msgid "Log out"
msgstr "Вийти"

#, python-format
msgid "Add %(name)s"
msgstr "Додати %(name)s"

msgid "History"
msgstr "Історія"

msgid "View on site"
msgstr "Дивитися на сайті"

msgid "Filter"
msgstr "Відфільтрувати"

msgid "Hide counts"
msgstr "Приховати кількість"

msgid "Show counts"
msgstr "Показати кількість"

msgid "Clear all filters"
msgstr "Очистити всі фільтри"

msgid "Remove from sorting"
msgstr "Видалити з сортування"

#, python-format
msgid "Sorting priority: %(priority_number)s"
msgstr "Пріорітет сортування: %(priority_number)s"

msgid "Toggle sorting"
msgstr "Сортувати в іншому напрямку"

msgid "Toggle theme (current theme: auto)"
msgstr "Перемкнути тему (поточна тема: автоматична)"

msgid "Toggle theme (current theme: light)"
msgstr "Перемкнути тему (поточна тема: світла)"

msgid "Toggle theme (current theme: dark)"
msgstr "Перемкнути тему (поточна тема: темна)"

#, python-format
msgid ""
"Deleting the %(object_name)s '%(escaped_object)s' would result in deleting "
"related objects, but your account doesn't have permission to delete the "
"following types of objects:"
msgstr ""
"Видалення %(object_name)s '%(escaped_object)s' призведе до видалення "
"пов'язаних об'єктів, але ваш реєстраційний запис не має дозволу видаляти "
"наступні типи об'єктів:"

#, python-format
msgid ""
"Deleting the %(object_name)s '%(escaped_object)s' would require deleting the "
"following protected related objects:"
msgstr ""
"Видалення %(object_name)s '%(escaped_object)s' вимагатиме видалення "
"наступних пов'язаних об'єктів:"

#, python-format
msgid ""
"Are you sure you want to delete the %(object_name)s \"%(escaped_object)s\"? "
"All of the following related items will be deleted:"
msgstr ""
"Ви впевнені, що хочете видалити %(object_name)s \"%(escaped_object)s\"? Всі "
"пов'язані записи, що перелічені, будуть видалені:"

msgid "Objects"
msgstr "Об'єкти"

msgid "Yes, I’m sure"
msgstr "Так, я впевнений"

msgid "No, take me back"
msgstr "Ні, повернутись назад"

#, python-format
msgid ""
"Deleting the selected %(objects_name)s would result in deleting related "
"objects, but your account doesn't have permission to delete the following "
"types of objects:"
msgstr ""
"Видалення обраних %(objects_name)s вимагатиме видалення пов'язаних об'єктів, "
"але ваш обліковий запис не має прав для видалення таких типів об'єктів:"

#, python-format
msgid ""
"Deleting the selected %(objects_name)s would require deleting the following "
"protected related objects:"
msgstr ""
"Видалення обраних %(objects_name)s вимагатиме видалення наступних захищених "
"пов'язаних об'єктів:"

#, python-format
msgid ""
"Are you sure you want to delete the selected %(objects_name)s? All of the "
"following objects and their related items will be deleted:"
msgstr ""
"Ви впевнені, що хочете видалити вибрані %(objects_name)s? Всі вказані "
"об'єкти та пов'язані з ними елементи будуть видалені:"

msgid "Delete?"
msgstr "Видалити?"

#, python-format
msgid " By %(filter_title)s "
msgstr "За %(filter_title)s"

msgid "Summary"
msgstr "Резюме"

msgid "Recent actions"
msgstr "Недавні дії"

msgid "My actions"
msgstr "Мої дії"

msgid "None available"
msgstr "Немає"

msgid "Added:"
msgstr "Додано:"

msgid "Changed:"
msgstr "Змінено:"

msgid "Deleted:"
msgstr "Видалено:"

msgid "Unknown content"
msgstr "Невідомий зміст"

msgid ""
"Something’s wrong with your database installation. Make sure the appropriate "
"database tables have been created, and make sure the database is readable by "
"the appropriate user."
msgstr ""
"Щось не так з інсталяцією бази даних. Запевніться, що певні таблиці бази "
"даних були створені і що вона може бути прочитана певним користувачем."

#, python-format
msgid ""
"You are authenticated as %(username)s, but are not authorized to access this "
"page. Would you like to login to a different account?"
msgstr ""
"Ви аутентифіковані як %(username)s, але вам не надано доступ до цієї "
"сторінки.\n"
"Ввійти в інший аккаунт?"

msgid "Forgotten your login credentials?"
msgstr ""

msgid "Toggle navigation"
msgstr "Увімкнути навігацію"

msgid "Sidebar"
msgstr "Бічна панель"

msgid "Start typing to filter…"
msgstr "Почніть писати для фільтру..."

msgid "Filter navigation items"
msgstr "Фільтрувати навігаційні об'єкти"

msgid "Date/time"
msgstr "Дата/час"

msgid "User"
msgstr "Користувач"

msgid "Action"
msgstr "Дія"

msgid "entry"
msgid_plural "entries"
msgstr[0] "запис"
msgstr[1] "записи"
msgstr[2] "записи"
msgstr[3] "записи"

msgid ""
"This object doesn’t have a change history. It probably wasn’t added via this "
"admin site."
msgstr ""
"Цей об'єкт не має історії змін. Напевно, він був доданий не через цей сайт "
"адміністрування."

msgid "Show all"
msgstr "Показати всі"

msgid "Save"
msgstr "Зберегти"

msgid "Popup closing…"
msgstr "Закриття спливаючого вікна"

msgid "Search"
msgstr "Пошук"

#, python-format
msgid "%(counter)s result"
msgid_plural "%(counter)s results"
msgstr[0] "%(counter)s результат"
msgstr[1] "%(counter)s результати"
msgstr[2] "%(counter)s результатів"
msgstr[3] "%(counter)s результатів"

#, python-format
msgid "%(full_result_count)s total"
msgstr "%(full_result_count)s всього"

msgid "Save as new"
msgstr "Зберегти як нове"

msgid "Save and add another"
msgstr "Зберегти і додати інше"

msgid "Save and continue editing"
msgstr "Зберегти і продовжити редагування"

msgid "Save and view"
msgstr "Зберегти і переглянути"

msgid "Close"
msgstr "Закрити"

#, python-format
msgid "Change selected %(model)s"
msgstr "Змінити обрану %(model)s"

#, python-format
msgid "Add another %(model)s"
msgstr "Додати ще одну %(model)s"

#, python-format
msgid "Delete selected %(model)s"
msgstr "Видалити обрану %(model)s"

#, python-format
msgid "View selected %(model)s"
msgstr "Переглянути обрану %(model)s"

msgid "Thanks for spending some quality time with the web site today."
msgstr "Дякуємо за час, який був проведений сьогодні на сайті."

msgid "Log in again"
msgstr "Увійти знову"

msgid "Password change"
msgstr "Зміна паролю"

msgid "Your password was changed."
msgstr "Ваш пароль було змінено."

msgid ""
"Please enter your old password, for security’s sake, and then enter your new "
"password twice so we can verify you typed it in correctly."
msgstr ""
"Будь ласка введіть ваш старий пароль, заради безпеки, після цього введіть "
"ваш новий пароль двічі для верифікації коректності написаного."

msgid "Change my password"
msgstr "Змінити мій пароль"

msgid "Password reset"
msgstr "Перевстановлення паролю"

msgid "Your password has been set.  You may go ahead and log in now."
msgstr "Пароль встановлено. Ви можете увійти зараз."

msgid "Password reset confirmation"
msgstr "Підтвердження перевстановлення паролю"

msgid ""
"Please enter your new password twice so we can verify you typed it in "
"correctly."
msgstr ""
"Будь ласка, введіть ваш старий пароль, задля безпеки, потім введіть ваш "
"новий пароль двічі для перевірки."

msgid "New password:"
msgstr "Новий пароль:"

msgid "Confirm password:"
msgstr "Підтвердіть пароль:"

msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a new password reset."
msgstr ""
"Посилання на перевстановлення паролю було помилковим. Можливо тому, що воно "
"було вже використано. Будь ласка, замовте нове перевстановлення паролю."

msgid ""
"We’ve emailed you instructions for setting your password, if an account "
"exists with the email you entered. You should receive them shortly."
msgstr ""
"Ми відправили вам інструкції для встановлення пароля, якщо обліковий запис з "
"введеною адресою існує. Ви маєте отримати їх найближчим часом."

msgid ""
"If you don’t receive an email, please make sure you’ve entered the address "
"you registered with, and check your spam folder."
msgstr ""
"Якщо Ви не отримали електронного листа, переконайтеся, будь ласка, в "
"зареєстрованій адресі і перевірте папку \"Спам\"."

#, python-format
msgid ""
"You're receiving this email because you requested a password reset for your "
"user account at %(site_name)s."
msgstr ""
"Ви отримали цей лист через те, що зробили запит на перевстановлення пароля "
"для облікового запису користувача на %(site_name)s."

msgid "Please go to the following page and choose a new password:"
msgstr "Будь ласка, перейдіть на цю сторінку, та оберіть новий пароль:"

msgid "In case you’ve forgotten, you are:"
msgstr ""

msgid "Thanks for using our site!"
msgstr "Дякуємо за користування нашим сайтом!"

#, python-format
msgid "The %(site_name)s team"
msgstr "Команда сайту %(site_name)s "

msgid ""
"Forgotten your password? Enter your email address below, and we’ll email "
"instructions for setting a new one."
msgstr ""
"Забули пароль? Введіть свою email-адресу нижче і ми відправимо вам "
"інструкції по встановленню нового."

msgid "Email address:"
msgstr "Email адреса:"

msgid "Reset my password"
msgstr "Перевстановіть мій пароль"

msgid "Select all objects on this page for an action"
msgstr "Обрати всі об'єкти на сторінці для дії"

msgid "All dates"
msgstr "Всі дати"

#, python-format
msgid "Select %s"
msgstr "Вибрати %s"

#, python-format
msgid "Select %s to change"
msgstr "Виберіть %s щоб змінити"

#, python-format
msgid "Select %s to view"
msgstr "Вибрати %s для перегляду"

msgid "Date:"
msgstr "Дата:"

msgid "Time:"
msgstr "Час:"

msgid "Lookup"
msgstr "Пошук"

msgid "Currently:"
msgstr "На даний час:"

msgid "Change:"
msgstr "Змінено:"
