{% extends 'restaurant/admin/base.html' %}

{% block title %}Tables - RestoQR Admin{% endblock %}
{% block page_title %}Tables{% endblock %}

{% block admin_content %}
<div class="space-y-6 fade-in">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div class="flex items-center space-x-4 mb-4 sm:mb-0">
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Tables</h1>
            <span class="bg-primary text-white px-3 py-1 rounded-full text-sm font-medium">
                {{ tables.count }} total
            </span>
        </div>
        
        <a href="{% url 'restaurant:table_add' %}" class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-secondary transition-colors duration-200 hover-scale">
            <i class="fas fa-plus mr-2"></i>
            Add Table
        </a>
    </div>
    
    <!-- Tables Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {% for table in tables %}
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 hover-scale">
            <!-- Table Icon -->
            <div class="flex items-center justify-center w-16 h-16 bg-primary rounded-full mx-auto mb-4">
                <i class="fas fa-chair text-white text-2xl"></i>
            </div>
            
            <!-- Table Info -->
            <div class="text-center">
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">Table {{ table.number }}</h3>
                <p class="text-gray-600 dark:text-gray-400 mb-4">
                    <i class="fas fa-users mr-1"></i>
                    Capacity: {{ table.capacity }} people
                </p>
                
                <!-- Status Badge -->
                <div class="mb-4">
                    {% if table.is_active %}
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                            <i class="fas fa-check-circle mr-1"></i>
                            Active
                        </span>
                    {% else %}
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                            <i class="fas fa-times-circle mr-1"></i>
                            Inactive
                        </span>
                    {% endif %}
                </div>
                
                <!-- Action Buttons -->
                <div class="space-y-2">
                    <div class="flex space-x-2">
                        <a href="{% url 'restaurant:table_edit' table.pk %}" 
                           class="flex-1 bg-blue-500 text-white text-center py-2 rounded-lg hover:bg-blue-600 transition-colors duration-200">
                            <i class="fas fa-edit mr-1"></i>
                            Edit
                        </a>
                        <button onclick="deleteTable({{ table.pk }}, {{ table.number }})" 
                                class="flex-1 bg-red-500 text-white py-2 rounded-lg hover:bg-red-600 transition-colors duration-200">
                            <i class="fas fa-trash mr-1"></i>
                            Delete
                        </button>
                    </div>
                    
                    <a href="{% url 'restaurant:generate_table_qr' table.pk %}" 
                       class="w-full bg-green-500 text-white text-center py-2 rounded-lg hover:bg-green-600 transition-colors duration-200 block">
                        <i class="fas fa-qrcode mr-1"></i>
                        Generate QR Code
                    </a>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-span-full text-center py-12">
            <i class="fas fa-chair text-gray-400 text-6xl mb-4"></i>
            <h3 class="text-xl font-medium text-gray-900 dark:text-white mb-2">No tables found</h3>
            <p class="text-gray-500 dark:text-gray-400 mb-4">Get started by adding your first table.</p>
            <a href="{% url 'restaurant:table_add' %}" class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-secondary transition-colors duration-200">
                <i class="fas fa-plus mr-2"></i>
                Add Table
            </a>
        </div>
        {% endfor %}
    </div>
    
    <!-- Quick Stats -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            <i class="fas fa-chart-bar mr-2 text-primary"></i>
            Table Statistics
        </h3>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="text-center">
                <div class="text-3xl font-bold text-primary mb-2">{{ tables.count }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">Total Tables</div>
            </div>
            
            <div class="text-center">
                <div class="text-3xl font-bold text-green-600 mb-2">
                    {% with active_count=tables|length %}
                        {% for table in tables %}
                            {% if table.is_active %}{{ forloop.counter0|add:1 }}{% endif %}
                        {% endfor %}
                    {% endwith %}
                </div>
                <div class="text-sm text-gray-600 dark:text-gray-400">Active Tables</div>
            </div>
            
            <div class="text-center">
                <div class="text-3xl font-bold text-blue-600 mb-2">
                    {% with total_capacity=0 %}
                        {% for table in tables %}
                            {% if table.is_active %}
                                {{ total_capacity|add:table.capacity }}
                            {% endif %}
                        {% endfor %}
                    {% endwith %}
                </div>
                <div class="text-sm text-gray-600 dark:text-gray-400">Total Capacity</div>
            </div>
        </div>
    </div>
</div>

<!-- Hidden CSRF Token -->
<form style="display: none;">
    {% csrf_token %}
</form>

<script>
    function deleteTable(tableId, tableNumber) {
        Swal.fire({
            title: 'Supprimer la Table ?',
            html: `
                <div class="text-center">
                    <div class="text-6xl mb-4">🪑</div>
                    <p class="text-lg mb-2">Êtes-vous sûr de vouloir supprimer</p>
                    <p class="font-bold text-red-600 text-xl">Table ${tableNumber}</p>
                    <p class="text-sm text-gray-500 mt-3">Cette action supprimera :</p>
                    <div class="mt-2 text-sm text-gray-600">
                        <div>• La table et ses paramètres</div>
                        <div>• Le QR code associé</div>
                        <div>• L'historique des commandes</div>
                    </div>
                    <p class="text-sm text-red-500 mt-3 font-semibold">Cette action est irréversible</p>
                </div>
            `,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#EF4444',
            cancelButtonColor: '#6B7280',
            confirmButtonText: '<i class="fas fa-trash mr-2"></i>Oui, supprimer !',
            cancelButtonText: '<i class="fas fa-times mr-2"></i>Annuler',
            reverseButtons: true,
            focusCancel: true,
            width: '500px',
            customClass: {
                popup: 'rounded-2xl',
                confirmButton: 'rounded-lg px-6 py-3 font-semibold',
                cancelButton: 'rounded-lg px-6 py-3 font-semibold'
            }
        }).then((result) => {
            if (result.isConfirmed) {
                // Show loading state
                Swal.fire({
                    title: 'Suppression en cours...',
                    html: `
                        <div class="text-center">
                            <i class="fas fa-spinner fa-spin text-4xl text-red-500 mb-4"></i>
                            <p class="text-lg">Suppression de la Table ${tableNumber}</p>
                            <p class="text-sm text-gray-500 mt-2">Veuillez patienter</p>
                        </div>
                    `,
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    showConfirmButton: false,
                    customClass: {
                        popup: 'rounded-2xl'
                    }
                });
                
                // Create a form and submit it
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `{% url 'restaurant:table_delete' 0 %}`.replace('0', tableId);
                
                // Get CSRF token
                const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
                const csrfInput = document.createElement('input');
                csrfInput.type = 'hidden';
                csrfInput.name = 'csrfmiddlewaretoken';
                csrfInput.value = csrfToken;
                form.appendChild(csrfInput);
                
                document.body.appendChild(form);
                form.submit();
            }
        });
    }
    
    // Add enhanced hover effects and animations
    document.addEventListener('DOMContentLoaded', function() {
        // Enhanced hover effects for table cards
        const tableCards = document.querySelectorAll('.hover-scale');
        tableCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px) scale(1.02)';
                this.style.boxShadow = '0 20px 40px rgba(0, 0, 0, 0.1)';
                this.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
                this.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
            });
        });
        
        // Enhanced button hover effects
        const editButtons = document.querySelectorAll('a[href*="edit"]');
        const deleteButtons = document.querySelectorAll('button[onclick*="deleteTable"]');
        const qrButtons = document.querySelectorAll('a[href*="generate"]');
        
        // Edit buttons (blue)
        editButtons.forEach(button => {
            button.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
                this.style.boxShadow = '0 8px 20px rgba(59, 130, 246, 0.4)';
                this.style.transition = 'all 0.3s ease';
            });
            
            button.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = 'none';
            });
        });
        
        // Delete buttons (red)
        deleteButtons.forEach(button => {
            button.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
                this.style.boxShadow = '0 8px 20px rgba(239, 68, 68, 0.4)';
                this.style.transition = 'all 0.3s ease';
            });
            
            button.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = 'none';
            });
            
            // Click animation
            button.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
            });
        });
        
        // QR buttons (green)
        qrButtons.forEach(button => {
            button.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
                this.style.boxShadow = '0 8px 20px rgba(34, 197, 94, 0.4)';
                this.style.transition = 'all 0.3s ease';
            });
            
            button.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = 'none';
            });
        });
        
        // Add pulse animation to active status badges
        const activebadges = document.querySelectorAll('.bg-green-100');
        activebadges.forEach(badge => {
            badge.style.animation = 'pulse 2s infinite';
        });
        
        // Add floating animation to table icons
        const tableIcons = document.querySelectorAll('.fa-chair');
        tableIcons.forEach((icon, index) => {
            icon.style.animation = `float 3s ease-in-out infinite ${index * 0.5}s`;
        });
    });
    
    // Add CSS animations
    const style = document.createElement('style');
    style.textContent = `
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }
        
        .table-card-enter {
            animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }
    `;
    document.head.appendChild(style);
</script>
{% endblock %}

