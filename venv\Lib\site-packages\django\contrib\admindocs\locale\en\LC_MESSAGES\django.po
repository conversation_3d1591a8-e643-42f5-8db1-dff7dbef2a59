# This file is distributed under the same license as the Django package.
#
msgid ""
msgstr ""
"Project-Id-Version: Django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2010-05-13 15:35+0200\n"
"Last-Translator: Django team\n"
"Language-Team: English <<EMAIL>>\n"
"Language: en\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: contrib/admindocs/apps.py:7
msgid "Administrative Documentation"
msgstr ""

#: contrib/admindocs/templates/admin_doc/bookmarklets.html:6
#: contrib/admindocs/templates/admin_doc/index.html:6
#: contrib/admindocs/templates/admin_doc/missing_docutils.html:6
#: contrib/admindocs/templates/admin_doc/model_detail.html:14
#: contrib/admindocs/templates/admin_doc/model_index.html:8
#: contrib/admindocs/templates/admin_doc/template_detail.html:6
#: contrib/admindocs/templates/admin_doc/template_filter_index.html:7
#: contrib/admindocs/templates/admin_doc/template_tag_index.html:7
#: contrib/admindocs/templates/admin_doc/view_detail.html:6
#: contrib/admindocs/templates/admin_doc/view_index.html:7
msgid "Home"
msgstr ""

#: contrib/admindocs/templates/admin_doc/bookmarklets.html:7
#: contrib/admindocs/templates/admin_doc/index.html:7
#: contrib/admindocs/templates/admin_doc/index.html:10
#: contrib/admindocs/templates/admin_doc/index.html:14
#: contrib/admindocs/templates/admin_doc/missing_docutils.html:7
#: contrib/admindocs/templates/admin_doc/missing_docutils.html:14
#: contrib/admindocs/templates/admin_doc/model_detail.html:15
#: contrib/admindocs/templates/admin_doc/model_index.html:9
#: contrib/admindocs/templates/admin_doc/template_detail.html:7
#: contrib/admindocs/templates/admin_doc/template_filter_index.html:8
#: contrib/admindocs/templates/admin_doc/template_tag_index.html:8
#: contrib/admindocs/templates/admin_doc/view_detail.html:7
#: contrib/admindocs/templates/admin_doc/view_index.html:8
msgid "Documentation"
msgstr ""

#: contrib/admindocs/templates/admin_doc/bookmarklets.html:8
#: contrib/admindocs/templates/admin_doc/index.html:29
msgid "Bookmarklets"
msgstr ""

#: contrib/admindocs/templates/admin_doc/bookmarklets.html:11
msgid "Documentation bookmarklets"
msgstr ""

#: contrib/admindocs/templates/admin_doc/bookmarklets.html:15
msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""

#: contrib/admindocs/templates/admin_doc/bookmarklets.html:22
msgid "Documentation for this page"
msgstr ""

#: contrib/admindocs/templates/admin_doc/bookmarklets.html:23
msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""

#: contrib/admindocs/templates/admin_doc/index.html:17
#: contrib/admindocs/templates/admin_doc/template_tag_index.html:9
msgid "Tags"
msgstr ""

#: contrib/admindocs/templates/admin_doc/index.html:18
msgid "List of all the template tags and their functions."
msgstr ""

#: contrib/admindocs/templates/admin_doc/index.html:20
#: contrib/admindocs/templates/admin_doc/template_filter_index.html:9
msgid "Filters"
msgstr ""

#: contrib/admindocs/templates/admin_doc/index.html:21
msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""

#: contrib/admindocs/templates/admin_doc/index.html:23
#: contrib/admindocs/templates/admin_doc/model_detail.html:16
#: contrib/admindocs/templates/admin_doc/model_index.html:10
#: contrib/admindocs/templates/admin_doc/model_index.html:14
msgid "Models"
msgstr ""

#: contrib/admindocs/templates/admin_doc/index.html:24
msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""

#: contrib/admindocs/templates/admin_doc/index.html:26
#: contrib/admindocs/templates/admin_doc/view_detail.html:8
#: contrib/admindocs/templates/admin_doc/view_index.html:9
#: contrib/admindocs/templates/admin_doc/view_index.html:12
msgid "Views"
msgstr ""

#: contrib/admindocs/templates/admin_doc/index.html:27
msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""

#: contrib/admindocs/templates/admin_doc/index.html:30
msgid "Tools for your browser to quickly access admin functionality."
msgstr ""

#: contrib/admindocs/templates/admin_doc/missing_docutils.html:10
msgid "Please install docutils"
msgstr ""

#: contrib/admindocs/templates/admin_doc/missing_docutils.html:17
#, python-format
msgid ""
"The admin documentation system requires Python’s <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""

#: contrib/admindocs/templates/admin_doc/missing_docutils.html:19
#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""

#: contrib/admindocs/templates/admin_doc/model_detail.html:21
#, python-format
msgid "Model: %(name)s"
msgstr ""

#: contrib/admindocs/templates/admin_doc/model_detail.html:30
msgid "Fields"
msgstr ""

#: contrib/admindocs/templates/admin_doc/model_detail.html:35
msgid "Field"
msgstr ""

#: contrib/admindocs/templates/admin_doc/model_detail.html:36
msgid "Type"
msgstr ""

#: contrib/admindocs/templates/admin_doc/model_detail.html:37
#: contrib/admindocs/templates/admin_doc/model_detail.html:60
msgid "Description"
msgstr ""

#: contrib/admindocs/templates/admin_doc/model_detail.html:53
msgid "Methods with arguments"
msgstr ""

#: contrib/admindocs/templates/admin_doc/model_detail.html:58
msgid "Method"
msgstr ""

#: contrib/admindocs/templates/admin_doc/model_detail.html:59
msgid "Arguments"
msgstr ""

#: contrib/admindocs/templates/admin_doc/model_detail.html:76
msgid "Back to Model documentation"
msgstr ""

#: contrib/admindocs/templates/admin_doc/model_index.html:18
msgid "Model documentation"
msgstr ""

#: contrib/admindocs/templates/admin_doc/model_index.html:43
msgid "Model groups"
msgstr ""

#: contrib/admindocs/templates/admin_doc/template_detail.html:8
msgid "Templates"
msgstr ""

#: contrib/admindocs/templates/admin_doc/template_detail.html:13
#, python-format
msgid "Template: %(name)s"
msgstr ""

#: contrib/admindocs/templates/admin_doc/template_detail.html:16
#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr ""

#. Translators: Search is not a verb here, it qualifies path (a search path)
#: contrib/admindocs/templates/admin_doc/template_detail.html:19
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr ""

#: contrib/admindocs/templates/admin_doc/template_detail.html:22
msgid "(does not exist)"
msgstr ""

#: contrib/admindocs/templates/admin_doc/template_detail.html:26
msgid "Back to Documentation"
msgstr ""

#: contrib/admindocs/templates/admin_doc/template_filter_index.html:12
msgid "Template filters"
msgstr ""

#: contrib/admindocs/templates/admin_doc/template_filter_index.html:16
msgid "Template filter documentation"
msgstr ""

#: contrib/admindocs/templates/admin_doc/template_filter_index.html:22
#: contrib/admindocs/templates/admin_doc/template_filter_index.html:43
msgid "Built-in filters"
msgstr ""

#: contrib/admindocs/templates/admin_doc/template_filter_index.html:23
#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""

#: contrib/admindocs/templates/admin_doc/template_tag_index.html:12
msgid "Template tags"
msgstr ""

#: contrib/admindocs/templates/admin_doc/template_tag_index.html:16
msgid "Template tag documentation"
msgstr ""

#: contrib/admindocs/templates/admin_doc/template_tag_index.html:22
#: contrib/admindocs/templates/admin_doc/template_tag_index.html:43
msgid "Built-in tags"
msgstr ""

#: contrib/admindocs/templates/admin_doc/template_tag_index.html:23
#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""

#: contrib/admindocs/templates/admin_doc/view_detail.html:12
#, python-format
msgid "View: %(name)s"
msgstr ""

#: contrib/admindocs/templates/admin_doc/view_detail.html:23
msgid "Context:"
msgstr ""

#: contrib/admindocs/templates/admin_doc/view_detail.html:28
msgid "Templates:"
msgstr ""

#: contrib/admindocs/templates/admin_doc/view_detail.html:32
msgid "Back to View documentation"
msgstr ""

#: contrib/admindocs/templates/admin_doc/view_index.html:16
msgid "View documentation"
msgstr ""

#: contrib/admindocs/templates/admin_doc/view_index.html:22
msgid "Jump to namespace"
msgstr ""

#: contrib/admindocs/templates/admin_doc/view_index.html:27
msgid "Empty namespace"
msgstr ""

#: contrib/admindocs/templates/admin_doc/view_index.html:40
#, python-format
msgid "Views by namespace %(name)s"
msgstr ""

#: contrib/admindocs/templates/admin_doc/view_index.html:42
msgid "Views by empty namespace"
msgstr ""

#: contrib/admindocs/templates/admin_doc/view_index.html:49
#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""

#: contrib/admindocs/views.py:72 contrib/admindocs/views.py:73
#: contrib/admindocs/views.py:75
msgid "tag:"
msgstr ""

#: contrib/admindocs/views.py:103 contrib/admindocs/views.py:104
#: contrib/admindocs/views.py:106
msgid "filter:"
msgstr ""

#: contrib/admindocs/views.py:162 contrib/admindocs/views.py:163
#: contrib/admindocs/views.py:165
msgid "view:"
msgstr ""

#: contrib/admindocs/views.py:192
#, python-format
msgid "App %(app_label)r not found"
msgstr ""

#: contrib/admindocs/views.py:196
#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr ""

#: contrib/admindocs/views.py:201 contrib/admindocs/views.py:202
#: contrib/admindocs/views.py:217 contrib/admindocs/views.py:240
#: contrib/admindocs/views.py:245 contrib/admindocs/views.py:260
#: contrib/admindocs/views.py:301 contrib/admindocs/views.py:306
msgid "model:"
msgstr ""

#: contrib/admindocs/views.py:213
#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr ""

#: contrib/admindocs/views.py:233 contrib/admindocs/views.py:293
#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr ""

#: contrib/admindocs/views.py:240 contrib/admindocs/views.py:301
#, python-format
msgid "all %s"
msgstr ""

#: contrib/admindocs/views.py:245 contrib/admindocs/views.py:306
#, python-format
msgid "number of %s"
msgstr ""

#: contrib/admindocs/views.py:398
#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr ""
