{% extends 'restaurant/admin/base.html' %}

{% block title %}Orders - RestoQR Admin{% endblock %}
{% block page_title %}Orders{% endblock %}

{% block admin_content %}
<div class="space-y-6 fade-in">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div class="flex items-center space-x-4 mb-4 sm:mb-0">
            <h1 class="text-2xl font-bold text-gray-900 dark:text-light_text">Orders</h1>
            <span class="bg-primary text-white px-3 py-1 rounded-full text-sm font-medium">
                {{ orders.count }} total
            </span>
        </div>
    </div>
    
    <!-- Filters -->
    <div class="bg-white dark:bg-dark_card rounded-xl shadow-sm p-6">
        <form method="GET" class="flex flex-wrap gap-4 items-end">
            <div class="flex-1 min-w-48">
                <label class="block text-sm font-medium text-gray-700 dark:text-light_text mb-2">
                    Filter by Status
                </label>
                <select name="status" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-light_text">
                    <option value="">All Statuses</option>
                    {% for value, label in status_choices %}
                    <option value="{{ value }}" {% if current_status == value %}selected{% endif %}>{{ label }}</option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="flex-1 min-w-48">
                <label class="block text-sm font-medium text-gray-700 dark:text-light_text mb-2">
                    Filter by Date
                </label>
                <input type="date" name="date" value="{{ current_date }}" 
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-light_text">
            </div>
            
            <div class="flex space-x-2">
                <button type="submit" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-secondary transition-colors duration-200">
                    <i class="fas fa-filter mr-2"></i>
                    Filter
                </button>
                <a href="{% url 'restaurant:order_list' %}" class="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors duration-200">
                    <i class="fas fa-times mr-2"></i>
                    Clear
                </a>
            </div>
        </form>
    </div>
    
    <!-- Orders Table -->
    <div class="bg-white dark:bg-dark_card rounded-xl shadow-sm overflow-hidden">
        {% if orders %}
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Order
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Table
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Items
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Total
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Status
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Time
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-dark_card divide-y divide-gray-200 dark:divide-gray-700">
                    {% for order in orders %}
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900 dark:text-light_text">
                                #{{ order.id }}
                            </div>
                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                {{ order.created_at|date:"M d, Y" }}
                            </div>
                        </td>
                        
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900 dark:text-light_text">
                                Table {{ order.table.number }}
                            </div>
                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                {{ order.table.capacity }} seats
                            </div>
                        </td>
                        
                        <td class="px-6 py-4">
                            <div class="text-sm text-gray-900 dark:text-light_text">
                                {{ order.items.count }} item{{ order.items.count|pluralize }}
                            </div>
                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                {% for item in order.items.all|slice:":2" %}
                                    {{ item.product.name }}{% if not forloop.last %}, {% endif %}
                                {% endfor %}
                                {% if order.items.count > 2 %}...{% endif %}
                            </div>
                        </td>
                        
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900 dark:text-light_text">
                                {{ order.total_amount }} dh
                            </div>
                        </td>
                        
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if order.status == 'pending' %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                                    <i class="fas fa-clock mr-1"></i>
                                    Pending
                                </span>
                            {% elif order.status == 'confirmed' %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                    <i class="fas fa-check mr-1"></i>
                                    Confirmed
                                </span>
                            {% elif order.status == 'preparing' %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200">
                                    <i class="fas fa-fire mr-1"></i>
                                    Preparing
                                </span>
                            {% elif order.status == 'ready' %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                    <i class="fas fa-bell mr-1"></i>
                                    Ready
                                </span>
                            {% elif order.status == 'completed' %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200">
                                    <i class="fas fa-check-circle mr-1"></i>
                                    Completed
                                </span>
                            {% endif %}
                        </td>
                        
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                            {{ order.created_at|date:"H:i" }}
                        </td>
                        
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                            <a href="{% url 'restaurant:order_detail' order.id %}" 
                               class="text-primary hover:text-secondary">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="{% url 'restaurant:download_invoice' order.id %}" 
                               class="text-green-600 hover:text-green-800">
                                <i class="fas fa-download"></i>
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-12">
            <i class="fas fa-receipt text-gray-400 text-6xl mb-4"></i>
            <h3 class="text-xl font-medium text-gray-900 dark:text-light_text mb-2">No orders found</h3>
            <p class="text-gray-500 dark:text-gray-400">Orders will appear here when customers place them.</p>
        </div>
        {% endif %}
    </div>
    
    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white dark:bg-dark_card rounded-xl shadow-sm p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-yellow-100 dark:bg-yellow-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-clock text-yellow-600 dark:text-yellow-400"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Pending</p>
                    <p class="text-2xl font-semibold text-gray-900 dark:text-light_text">
                        {{ orders|length }}
                    </p>
                </div>
            </div>
        </div>
        
        <div class="bg-white dark:bg-dark_card rounded-xl shadow-sm p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-fire text-orange-600 dark:text-orange-400"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Preparing</p>
                    <p class="text-2xl font-semibold text-gray-900 dark:text-light_text">
                        {{ orders|length }}
                    </p>
                </div>
            </div>
        </div>
        
        <div class="bg-white dark:bg-dark_card rounded-xl shadow-sm p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-bell text-green-600 dark:text-green-400"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Ready</p>
                    <p class="text-2xl font-semibold text-gray-900 dark:text-light_text">
                        {{ orders|length }}
                    </p>
                </div>
            </div>
        </div>
        
        <div class="bg-white dark:bg-dark_card rounded-xl shadow-sm p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-dollar-sign text-blue-600 dark:text-blue-400"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Revenue</p>
                    <p class="text-2xl font-semibold text-gray-900 dark:text-light_text">
                        ${{ orders|length }}
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Auto-refresh every 30 seconds
    setInterval(() => {
        location.reload();
    }, 30000);
</script>
{% endblock %}

