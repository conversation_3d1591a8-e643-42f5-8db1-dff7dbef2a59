{% extends 'restaurant/base.html' %}

{% block title %}Connexion Admin - RestoQR{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 flex items-center justify-center p-4">
    <!-- Background Animation -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
        <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-r from-purple-400 to-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse" style="animation-delay: 2s;"></div>
        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-gradient-to-r from-indigo-400 to-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse" style="animation-delay: 4s;"></div>
    </div>
    
    <div class="relative z-10 w-full max-w-md">
        <!-- Login Card -->
        <div class="backdrop-blur-xl bg-white/80 dark:bg-gray-900/80 rounded-3xl shadow-2xl border border-white/20 dark:border-gray-700/20 overflow-hidden">
            <!-- Header Section -->
            <div class="relative px-8 pt-8 pb-6 text-center">
                <!-- Logo with Animation -->
                <div class="relative mx-auto w-20 h-20 mb-6">
                    <div class="absolute inset-0 bg-gradient-to-r from-blue-500 via-purple-500 to-indigo-600 rounded-2xl animate-pulse"></div>
                    <div class="relative w-full h-full bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-700 rounded-2xl flex items-center justify-center shadow-lg">
                        <i class="fas fa-qrcode text-white text-2xl"></i>
                    </div>
                    <!-- Floating Elements -->
                    <div class="absolute -top-2 -right-2 w-4 h-4 bg-yellow-400 rounded-full animate-bounce"></div>
                    <div class="absolute -bottom-1 -left-1 w-3 h-3 bg-green-400 rounded-full animate-ping"></div>
                </div>
                
                <!-- Title -->
                <h1 class="text-3xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 dark:from-white dark:via-blue-200 dark:to-purple-200 bg-clip-text text-transparent mb-2">
                    RestoQR Admin
                </h1>
                <p class="text-gray-600 dark:text-gray-300 text-sm">
                    Connectez-vous à votre panel d'administration
                </p>
            </div>
            
            <!-- Form Section -->
            <div class="px-8 pb-8">
                <form method="post" class="space-y-6" id="loginForm">
                    {% csrf_token %}
                    
                    <!-- Username Field -->
                    <div class="space-y-2">
                        <label for="username" class="block text-sm font-semibold text-gray-700 dark:text-gray-200">
                            <i class="fas fa-user mr-2 text-blue-500"></i>Nom d'utilisateur
                        </label>
                        <div class="relative group">
                            <input 
                                id="username" 
                                name="username" 
                                type="text" 
                                required 
                                class="w-full px-4 py-3 pl-12 bg-gray-50/50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                                placeholder="Entrez votre nom d'utilisateur"
                                autocomplete="username"
                            >
                            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                <i class="fas fa-user text-gray-400 group-focus-within:text-blue-500 transition-colors duration-300"></i>
                            </div>
                            <!-- Focus Ring -->
                            <div class="absolute inset-0 rounded-xl ring-2 ring-blue-500 ring-opacity-0 group-focus-within:ring-opacity-20 transition-all duration-300 pointer-events-none"></div>
                        </div>
                    </div>
                    
                    <!-- Password Field -->
                    <div class="space-y-2">
                        <label for="password" class="block text-sm font-semibold text-gray-700 dark:text-gray-200">
                            <i class="fas fa-lock mr-2 text-purple-500"></i>Mot de passe
                        </label>
                        <div class="relative group">
                            <input 
                                id="password" 
                                name="password" 
                                type="password" 
                                required 
                                class="w-full px-4 py-3 pl-12 pr-12 bg-gray-50/50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                                placeholder="Entrez votre mot de passe"
                                autocomplete="current-password"
                            >
                            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                <i class="fas fa-lock text-gray-400 group-focus-within:text-purple-500 transition-colors duration-300"></i>
                            </div>
                            <!-- Password Toggle -->
                            <button 
                                type="button" 
                                class="absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-300"
                                onclick="togglePassword()"
                            >
                                <i id="passwordToggleIcon" class="fas fa-eye"></i>
                            </button>
                            <!-- Focus Ring -->
                            <div class="absolute inset-0 rounded-xl ring-2 ring-purple-500 ring-opacity-0 group-focus-within:ring-opacity-20 transition-all duration-300 pointer-events-none"></div>
                        </div>
                    </div>
                    
                    <!-- Remember Me -->
                    <div class="flex items-center justify-between">
                        <label class="flex items-center">
                            <input type="checkbox" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                            <span class="ml-2 text-sm text-gray-600 dark:text-gray-300">Se souvenir de moi</span>
                        </label>
                        <a href="#" class="text-sm text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300 transition-colors duration-300">
                            Mot de passe oublié ?
                        </a>
                    </div>
                    
                    <!-- Submit Button -->
                    <button 
                        type="submit" 
                        class="w-full relative overflow-hidden bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 hover:from-blue-700 hover:via-purple-700 hover:to-indigo-700 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-[1.02] hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900"
                        id="submitBtn"
                    >
                        <!-- Button Background Animation -->
                        <div class="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-400 opacity-0 hover:opacity-20 transition-opacity duration-300"></div>
                        
                        <!-- Button Content -->
                        <span class="relative flex items-center justify-center">
                            <i id="submitIcon" class="fas fa-sign-in-alt mr-2"></i>
                            <span id="submitText">Se connecter</span>
                        </span>
                        
                        <!-- Loading Spinner (Hidden by default) -->
                        <div id="loadingSpinner" class="absolute inset-0 flex items-center justify-center opacity-0 transition-opacity duration-300">
                            <i class="fas fa-spinner animate-spin text-white"></i>
                        </div>
                    </button>
                </form>
                
                <!-- Demo Credentials -->
                <div class="mt-6 p-4 bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20 rounded-xl border border-amber-200 dark:border-amber-700/50">
                    <div class="flex items-center mb-2">
                        <i class="fas fa-info-circle text-amber-600 dark:text-amber-400 mr-2"></i>
                        <span class="text-sm font-semibold text-amber-800 dark:text-amber-200">Identifiants de démonstration</span>
                    </div>
                    <div class="text-xs text-amber-700 dark:text-amber-300 space-y-1">
                        <div><strong>Utilisateur :</strong> <code class="bg-amber-100 dark:bg-amber-800/50 px-2 py-1 rounded">achraf1</code></div>
                        <div><strong>Mot de passe :</strong> <code class="bg-amber-100 dark:bg-amber-800/50 px-2 py-1 rounded">[Votre mot de passe]</code></div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Footer -->
        <div class="text-center mt-8">
            <p class="text-sm text-gray-500 dark:text-gray-400">
                © 2025 RestoQR. Tous droits réservés.
            </p>
            <div class="flex justify-center space-x-4 mt-2">
                <a href="{% url 'restaurant:home' %}" class="text-xs text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-300">
                    <i class="fas fa-home mr-1"></i>Accueil
                </a>
                <a href="#" class="text-xs text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-300">
                    <i class="fas fa-question-circle mr-1"></i>Aide
                </a>
                <a href="#" class="text-xs text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-300">
                    <i class="fas fa-shield-alt mr-1"></i>Sécurité
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced JavaScript -->
<script>
    // Password toggle functionality
    function togglePassword() {
        const passwordInput = document.getElementById('password');
        const toggleIcon = document.getElementById('passwordToggleIcon');
        
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleIcon.classList.remove('fa-eye');
            toggleIcon.classList.add('fa-eye-slash');
        } else {
            passwordInput.type = 'password';
            toggleIcon.classList.remove('fa-eye-slash');
            toggleIcon.classList.add('fa-eye');
        }
    }
    
    // Form submission with loading state
    document.getElementById('loginForm').addEventListener('submit', function(e) {
        const submitBtn = document.getElementById('submitBtn');
        const submitIcon = document.getElementById('submitIcon');
        const submitText = document.getElementById('submitText');
        const loadingSpinner = document.getElementById('loadingSpinner');
        
        // Show loading state
        submitBtn.disabled = true;
        submitIcon.style.opacity = '0';
        submitText.textContent = 'Connexion en cours...';
        loadingSpinner.style.opacity = '1';
        
        // Add loading class for additional styling
        submitBtn.classList.add('cursor-not-allowed');
    });
    
    // Input focus effects
    document.addEventListener('DOMContentLoaded', function() {
        const inputs = document.querySelectorAll('input[type="text"], input[type="password"]');
        
        inputs.forEach(input => {
            // Focus effect
            input.addEventListener('focus', function() {
                this.parentElement.classList.add('scale-[1.02]');
            });
            
            // Blur effect
            input.addEventListener('blur', function() {
                this.parentElement.classList.remove('scale-[1.02]');
            });
            
            // Input validation feedback
            input.addEventListener('input', function() {
                if (this.value.length > 0) {
                    this.classList.add('border-green-300', 'dark:border-green-600');
                    this.classList.remove('border-gray-200', 'dark:border-gray-600');
                } else {
                    this.classList.remove('border-green-300', 'dark:border-green-600');
                    this.classList.add('border-gray-200', 'dark:border-gray-600');
                }
            });
        });
        
        // Auto-fill demo credentials (for development)
        const usernameInput = document.getElementById('username');
        const passwordInput = document.getElementById('password');
        
        // Add demo fill button functionality
        const demoCredentials = document.querySelector('.bg-gradient-to-r.from-amber-50');
        if (demoCredentials) {
            demoCredentials.addEventListener('click', function() {
                usernameInput.value = 'achraf1';
                usernameInput.dispatchEvent(new Event('input'));
                
                // Focus on password field
                passwordInput.focus();
                
                // Show success message
                const Toast = Swal.mixin({
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 2000,
                    timerProgressBar: true,
                });
                
                Toast.fire({
                    icon: 'info',
                    title: 'Nom d\'utilisateur rempli automatiquement'
                });
            });
            
            // Add cursor pointer
            demoCredentials.classList.add('cursor-pointer', 'hover:bg-gradient-to-r', 'hover:from-amber-100', 'hover:to-orange-100', 'dark:hover:from-amber-900/30', 'dark:hover:to-orange-900/30', 'transition-all', 'duration-300');
        }
    });
    
    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Alt + D to fill demo credentials
        if (e.altKey && e.key === 'd') {
            e.preventDefault();
            document.getElementById('username').value = 'achraf1';
            document.getElementById('password').focus();
        }
        
        // Enter to submit form
        if (e.key === 'Enter' && (e.target.tagName === 'INPUT')) {
            e.preventDefault();
            document.getElementById('loginForm').dispatchEvent(new Event('submit'));
        }
    });
    
    // Add particle effect on successful login (if needed)
    function createParticles() {
        for (let i = 0; i < 50; i++) {
            const particle = document.createElement('div');
            particle.className = 'absolute w-2 h-2 bg-blue-500 rounded-full opacity-70';
            particle.style.left = Math.random() * 100 + '%';
            particle.style.top = Math.random() * 100 + '%';
            particle.style.animation = `float ${Math.random() * 3 + 2}s ease-in-out infinite`;
            particle.style.animationDelay = Math.random() * 2 + 's';
            document.body.appendChild(particle);
            
            setTimeout(() => {
                particle.remove();
            }, 5000);
        }
    }
    
    // Error handling for failed login
    {% if messages %}
        {% for message in messages %}
            {% if message.tags == 'error' %}
                // Shake animation for error
                const loginCard = document.querySelector('.backdrop-blur-xl');
                loginCard.classList.add('animate-pulse');
                
                setTimeout(() => {
                    loginCard.classList.remove('animate-pulse');
                }, 1000);
                
                // Reset form state
                const submitBtn = document.getElementById('submitBtn');
                const submitIcon = document.getElementById('submitIcon');
                const submitText = document.getElementById('submitText');
                const loadingSpinner = document.getElementById('loadingSpinner');
                
                submitBtn.disabled = false;
                submitIcon.style.opacity = '1';
                submitText.textContent = 'Se connecter';
                loadingSpinner.style.opacity = '0';
                submitBtn.classList.remove('cursor-not-allowed');
            {% endif %}
        {% endfor %}
    {% endif %}
</script>

<style>
    /* Additional custom styles */
    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-10px) rotate(180deg); }
    }
    
    /* Smooth transitions for all interactive elements */
    * {
        transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
        transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
        transition-duration: 300ms;
    }
    
    /* Custom scrollbar */
    ::-webkit-scrollbar {
        width: 6px;
    }
    
    ::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.1);
        border-radius: 3px;
    }
    
    ::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.3);
        border-radius: 3px;
    }
    
    ::-webkit-scrollbar-thumb:hover {
        background: rgba(0, 0, 0, 0.5);
    }
</style>
{% endblock %}