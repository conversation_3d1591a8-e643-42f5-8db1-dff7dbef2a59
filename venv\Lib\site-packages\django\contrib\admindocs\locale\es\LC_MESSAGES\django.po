# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2012-2013
# <PERSON> <<EMAIL>>, 2014
# <PERSON> <<EMAIL>>, 2017
# e4db27214f7e7544f2022c647b585925_bb0e321, 2015
# <PERSON><PERSON> <<EMAIL>>, 2013
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON> <al<PERSON><EMAIL>>, 2016
# <PERSON><PERSON><PERSON><PERSON> Guerra <<EMAIL>>, 2014
# <PERSON><PERSON> <<EMAIL>>, 2020-2021
# <AUTHOR> <EMAIL>, 2015
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-02-11 05:39+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (http://www.transifex.com/django/django/language/"
"es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: es\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Administrative Documentation"
msgstr "Documentación Administrativa"

msgid "Home"
msgstr "Inicio"

msgid "Documentation"
msgstr "Documentación"

msgid "Bookmarklets"
msgstr "Bookmarklets"

msgid "Documentation bookmarklets"
msgstr "Bookmarklets de documentación"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"Para instalar bookmarklets, arrastre el enlace a su barra de favoritos, o "
"pulse con el botón derecho el enlace y añádalo a sus favoritos. Ahora puede "
"seleccionar el bookmarklet desde cualquier página del sitio. "

msgid "Documentation for this page"
msgstr "Documentación para esta página"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"Lo lleva desde cualquier página a la documentación de la vista que la genera."

msgid "Tags"
msgstr "Etiquetas"

msgid "List of all the template tags and their functions."
msgstr "Lista de todas la etiquetas de plantillas y sus funciones."

msgid "Filters"
msgstr "Filtros"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"Los filtros son acciones que se pueden aplicar a variables en una plantilla "
"para alterar el resultado."

msgid "Models"
msgstr "Modelos"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"Los modelos son descripciones de todos los objetos en el sistema y sus "
"campos asociados. Cada modelo tienen una lista de campos a los que se puede "
"acceder como variables de plantilla"

msgid "Views"
msgstr "Vistas"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"Cada página en el sitio público se genera a través de una vista.  La vista "
"define que plantilla se usa para generar la página y que objetos están "
"disponibles para esa plantilla."

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""
"Herramientas para el navegador para acceder a funciones de administración "
"rápidamente."

msgid "Please install docutils"
msgstr "Por favor, instale docutils"

#, python-format
msgid ""
"The admin documentation system requires Python’s <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""
"El sistema de documentación del administrador requiere la librería de Python "
"<a href=\"%(link)s\">docutils</a>."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""
"Por favor, pida al administrador que instale <a href=\"%(link)s\">docutils</"
"a>."

#, python-format
msgid "Model: %(name)s"
msgstr "Modelo: %(name)s"

msgid "Fields"
msgstr "Campos"

msgid "Field"
msgstr "Campo"

msgid "Type"
msgstr "Tipo"

msgid "Description"
msgstr "Descripción"

msgid "Methods with arguments"
msgstr "Métodos con argumentos"

msgid "Method"
msgstr "Método"

msgid "Arguments"
msgstr "Argumentos"

msgid "Back to Model documentation"
msgstr "Volver a la documentación de modelos"

msgid "Model documentation"
msgstr "Documentación de modelos"

msgid "Model groups"
msgstr "Grupo de modelos"

msgid "Templates"
msgstr "Plantillas"

#, python-format
msgid "Template: %(name)s"
msgstr "Plantilla: %(name)s"

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "Plantilla: <q>%(name)s</q>"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "Buscar ruta de la plantilla <q>%(name)s</q>:"

msgid "(does not exist)"
msgstr "(no existe)"

msgid "Back to Documentation"
msgstr "Volver a la documentación"

msgid "Template filters"
msgstr "Filtros de plantilla"

msgid "Template filter documentation"
msgstr "Documentación de los filtros de plantilla"

msgid "Built-in filters"
msgstr "Filtros integrados"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"Para utilizar estos filtros, incluya <code>%(code)s</code> en su plantilla "
"antes de usar el filtro."

msgid "Template tags"
msgstr "Etiquetas de plantilla"

msgid "Template tag documentation"
msgstr "Documentación de las etiquetas de plantilla"

msgid "Built-in tags"
msgstr "Etiquetas integradas"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"Para utilizar estas etiquetas, incluya <code>%(code)s</code> en su plantilla "
"antes de utilizar la etiqueta."

#, python-format
msgid "View: %(name)s"
msgstr "Vista: %(name)s"

msgid "Context:"
msgstr "Contexto:"

msgid "Templates:"
msgstr "Plantillas:"

msgid "Back to View documentation"
msgstr "Volver a la documentación de vistas"

msgid "View documentation"
msgstr "Documentación de vistas"

msgid "Jump to namespace"
msgstr "Ir al espacio de nombres"

msgid "Empty namespace"
msgstr "Espacio de nombres vacío"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "Visualizaciones por espacio de nombres %(name)s"

msgid "Views by empty namespace"
msgstr "Visualizaciones por espacio de nombres vacío"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"Visualizar función: <code>%(full_name)s</code>. Nombre: <code>%(url_name)s</"
"code>.\n"

msgid "tag:"
msgstr "etiqueta:"

msgid "filter:"
msgstr "filtro:"

msgid "view:"
msgstr "vista:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "Aplicación %(app_label)r no encontrada"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr ""
"El modelo %(model_name)r no se ha encontrado en la aplicación %(app_label)r"

msgid "model:"
msgstr "modelo:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "el objeto relacionado `%(app_label)s.%(data_type)s`"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "los objetos relacionados `%(app_label)s.%(object_name)s`"

#, python-format
msgid "all %s"
msgstr "todo %s"

#, python-format
msgid "number of %s"
msgstr "número de %s"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s no parece ser un objeto urlpattern"
