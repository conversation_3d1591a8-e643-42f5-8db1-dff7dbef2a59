from django.core.management.base import BaseCommand
from restaurant.models import Category, Product, Table, RestaurantSettings
from decimal import Decimal


class Command(BaseCommand):
    help = 'Seed the database with sample data'

    def handle(self, *args, **options):
        self.stdout.write('Seeding database with sample data...')
        
        # Create restaurant settings
        settings, created = RestaurantSettings.objects.get_or_create(
            defaults={
                'name': 'RestoQR Restaurant',
                'phone': '****** 567 8900',
                'email': '<EMAIL>',
                'address': '123 Restaurant Street, Food City, FC 12345',
                'currency_symbol': '$',
                'tax_rate': Decimal('8.50'),
                'service_charge': Decimal('10.00'),
            }
        )
        if created:
            self.stdout.write('✓ Restaurant settings created')
        
        # Create categories
        categories_data = [
            {'name': 'Appetizers', 'description': 'Start your meal with our delicious appetizers'},
            {'name': 'Main Courses', 'description': 'Hearty and satisfying main dishes'},
            {'name': 'Desserts', 'description': 'Sweet treats to end your meal'},
            {'name': 'Beverages', 'description': 'Refreshing drinks and beverages'},
            {'name': 'Salads', 'description': 'Fresh and healthy salad options'},
        ]
        
        categories = {}
        for cat_data in categories_data:
            category, created = Category.objects.get_or_create(
                name=cat_data['name'],
                defaults={'description': cat_data['description']}
            )
            categories[cat_data['name']] = category
            if created:
                self.stdout.write(f'✓ Category "{cat_data["name"]}" created')
        
        # Create products
        products_data = [
            # Appetizers
            {
                'name': 'Caesar Salad',
                'description': 'Fresh romaine lettuce with parmesan cheese, croutons, and caesar dressing',
                'price': Decimal('12.99'),
                'category': 'Appetizers',
                'preparation_time': 10,
            },
            {
                'name': 'Buffalo Wings',
                'description': 'Spicy chicken wings served with blue cheese dip and celery sticks',
                'price': Decimal('14.99'),
                'category': 'Appetizers',
                'preparation_time': 15,
            },
            {
                'name': 'Mozzarella Sticks',
                'description': 'Golden fried mozzarella sticks with marinara sauce',
                'price': Decimal('9.99'),
                'category': 'Appetizers',
                'preparation_time': 12,
            },
            
            # Main Courses
            {
                'name': 'Grilled Salmon',
                'description': 'Fresh Atlantic salmon grilled to perfection with lemon herb butter',
                'price': Decimal('24.99'),
                'category': 'Main Courses',
                'preparation_time': 20,
            },
            {
                'name': 'Ribeye Steak',
                'description': '12oz prime ribeye steak cooked to your preference with garlic mashed potatoes',
                'price': Decimal('32.99'),
                'category': 'Main Courses',
                'preparation_time': 25,
            },
            {
                'name': 'Chicken Parmesan',
                'description': 'Breaded chicken breast topped with marinara sauce and melted mozzarella',
                'price': Decimal('19.99'),
                'category': 'Main Courses',
                'preparation_time': 18,
            },
            {
                'name': 'Vegetarian Pasta',
                'description': 'Penne pasta with seasonal vegetables in a creamy alfredo sauce',
                'price': Decimal('16.99'),
                'category': 'Main Courses',
                'preparation_time': 15,
            },
            
            # Salads
            {
                'name': 'Greek Salad',
                'description': 'Mixed greens with feta cheese, olives, tomatoes, and Greek dressing',
                'price': Decimal('13.99'),
                'category': 'Salads',
                'preparation_time': 8,
            },
            {
                'name': 'Cobb Salad',
                'description': 'Mixed greens with bacon, blue cheese, hard-boiled egg, and avocado',
                'price': Decimal('15.99'),
                'category': 'Salads',
                'preparation_time': 10,
            },
            
            # Desserts
            {
                'name': 'Chocolate Cake',
                'description': 'Rich chocolate layer cake with chocolate ganache frosting',
                'price': Decimal('8.99'),
                'category': 'Desserts',
                'preparation_time': 5,
            },
            {
                'name': 'Tiramisu',
                'description': 'Classic Italian dessert with coffee-soaked ladyfingers and mascarpone',
                'price': Decimal('9.99'),
                'category': 'Desserts',
                'preparation_time': 5,
            },
            {
                'name': 'Ice Cream Sundae',
                'description': 'Vanilla ice cream with chocolate sauce, whipped cream, and cherry',
                'price': Decimal('6.99'),
                'category': 'Desserts',
                'preparation_time': 3,
            },
            
            # Beverages
            {
                'name': 'Fresh Orange Juice',
                'description': 'Freshly squeezed orange juice',
                'price': Decimal('4.99'),
                'category': 'Beverages',
                'preparation_time': 2,
            },
            {
                'name': 'Coffee',
                'description': 'Freshly brewed coffee',
                'price': Decimal('2.99'),
                'category': 'Beverages',
                'preparation_time': 3,
            },
            {
                'name': 'Soft Drinks',
                'description': 'Coca-Cola, Pepsi, Sprite, or other soft drinks',
                'price': Decimal('2.49'),
                'category': 'Beverages',
                'preparation_time': 1,
            },
        ]
        
        for product_data in products_data:
            category = categories[product_data['category']]
            product, created = Product.objects.get_or_create(
                name=product_data['name'],
                defaults={
                    'description': product_data['description'],
                    'price': product_data['price'],
                    'category': category,
                    'preparation_time': product_data['preparation_time'],
                    'is_available': True,
                }
            )
            if created:
                self.stdout.write(f'✓ Product "{product_data["name"]}" created')
        
        # Create tables
        for table_number in range(1, 11):  # Create tables 1-10
            table, created = Table.objects.get_or_create(
                number=table_number,
                defaults={
                    'capacity': 4,
                    'qr_code': f'table_{table_number}',
                    'is_active': True,
                }
            )
            if created:
                self.stdout.write(f'✓ Table {table_number} created')
        
        self.stdout.write(
            self.style.SUCCESS('Successfully seeded database with sample data!')
        )

