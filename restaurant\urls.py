from django.urls import path, include
from django.contrib.auth import views as auth_views

from . import views

app_name = 'restaurant'

urlpatterns = [
    # Home page
    path('', views.home, name='home'),
    
    # Admin routes
    path('admin/', views.admin_dashboard, name='admin_dashboard'),
    path('admin/login/', views.admin_login, name='admin_login'),
    path('admin/logout/', views.admin_logout, name='admin_logout'),
    path('accounts/login/', auth_views.LoginView.as_view(template_name='login.html'), name='login'),

    
    # Product management
    path('admin/products/', views.product_list, name='product_list'),
    path('admin/products/add/', views.product_add, name='product_add'),
    path('admin/products/<int:pk>/edit/', views.product_edit, name='product_edit'),
    path('admin/products/<int:pk>/delete/', views.product_delete, name='product_delete'),
    
    # Category management
    path('admin/categories/', views.category_list, name='category_list'),
    path('admin/categories/add/', views.category_add, name='category_add'),
    path('admin/categories/<int:pk>/edit/', views.category_edit, name='category_edit'),
    path('admin/categories/<int:pk>/delete/', views.category_delete, name='category_delete'),
    
    # Table management
    path('admin/tables/', views.table_list, name='table_list'),
    path('admin/tables/add/', views.table_add, name='table_add'),
    path('admin/tables/<int:pk>/edit/', views.table_edit, name='table_edit'),
    path('admin/tables/<int:pk>/delete/', views.table_delete, name='table_delete'),
    path('admin/tables/<int:pk>/qr/', views.generate_table_qr, name='generate_table_qr'),
    
    # Order management
    path('admin/orders/', views.order_list, name='order_list'),
    path("admin/orders/<uuid:order_id>/", views.order_detail, name="order_detail"),
    path("admin/orders/<uuid:order_id>/status/", views.update_order_status, name="update_order_status"),
    path("admin/orders/<uuid:order_id>/invoice/", views.download_invoice, name="download_invoice"),
    
    # Settings
    path('admin/settings/', views.restaurant_settings, name='restaurant_settings'),
    path('admin/profile/', views.admin_profile, name='admin_profile'),
    
    # Client interface (QR code access)
    path('qr/<int:table_id>/', views.client_menu, name='client_menu'),
    path('qr/<int:table_id>/thank-you/', views.client_thank_you, name='client_thank_you'),
    path("invoice/<uuid:order_id>/", views.client_download_invoice, name="client_download_invoice"),
    path("order-status/<uuid:order_id>/", views.client_order_status, name="client_order_status"),
    
    # API Endpoints
    path("api/place-order/", views.api_place_order, name="api_place_order"),
    path('api/order-status/<int:order_id>/', views.api_order_status, name='api_order_status'),
]

