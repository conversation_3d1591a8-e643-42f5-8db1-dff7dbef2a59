{% extends 'restaurant/admin/base.html' %}

{% block title %}Order #{{ order.id }} - RestoQR Admin{% endblock %}
{% block page_title %}Order #{{ order.id }}{% endblock %}

{% block admin_content %}
<div class="space-y-6 fade-in">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <a href="{% url 'restaurant:order_list' %}" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                <i class="fas fa-arrow-left text-xl"></i>
            </a>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-light_text">Order #{{ order.id }}</h1>
            {% if order.status == 'pending' %}
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                    <i class="fas fa-clock mr-1"></i>
                    Pending
                </span>
            {% elif order.status == 'confirmed' %}
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                    <i class="fas fa-check mr-1"></i>
                    Confirmed
                </span>
            {% elif order.status == 'preparing' %}
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200">
                    <i class="fas fa-fire mr-1"></i>
                    Preparing
                </span>
            {% elif order.status == 'ready' %}
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                    <i class="fas fa-bell mr-1"></i>
                    Ready
                </span>
            {% elif order.status == 'completed' %}
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200">
                    <i class="fas fa-check-circle mr-1"></i>
                    Completed
                </span>
            {% endif %}
        </div>
        
        <div class="flex space-x-3">
            <a href="{% url 'restaurant:download_invoice' order.id %}" 
               class="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors duration-200">
                <i class="fas fa-download mr-2"></i>
                Download Invoice
            </a>
        </div>
    </div>
    
    <!-- Order Information Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Order Details -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Order Items -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    <i class="fas fa-list mr-2 text-primary"></i>
                    Order Items
                </h3>
                
                <div class="space-y-4">
                    {% for item in order.items.all %}
                    <div class="flex items-center justify-between py-3 border-b border-gray-200 dark:border-gray-700 last:border-b-0">
                        <div class="flex items-center space-x-4">
                            <div class="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                                {% if item.product.image %}
                                    <img src="{{ item.product.image.url }}" alt="{{ item.product.name }}" class="w-full h-full object-cover rounded-lg">
                                {% else %}
                                    <i class="fas fa-utensils text-gray-400"></i>
                                {% endif %}
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900 dark:text-white">{{ item.product.name }}</h4>
                                <p class="text-sm text-gray-600 dark:text-gray-400">
                                    {{ item.price }} dh × {{ item.quantity }}
                                </p>
                                <p class="text-xs text-gray-500 dark:text-gray-500">
                                    <i class="fas fa-clock mr-1"></i>
                                    {{ item.product.prep_time }} min prep time
                                </p>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="font-semibold text-gray-900 dark:text-white">
                                {{ item.subtotal }} dh
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                    
                    <!-- Total -->
                    <div class="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
                        <span class="text-lg font-semibold text-gray-900 dark:text-white">Total:</span>
                        <span class="text-xl font-bold text-primary">{{ order.total_amount }} dh</span>
                    </div>
                </div>
            </div>
            
            <!-- Special Instructions -->
            {% if order.notes %}
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    <i class="fas fa-sticky-note mr-2 text-primary"></i>
                    Special Instructions
                </h3>
                <p class="text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    {{ order.notes }}
                </p>
            </div>
            {% endif %}
        </div>
        
        <!-- Order Status & Actions -->
        <div class="space-y-6">
            <!-- Order Information -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    <i class="fas fa-info-circle mr-2 text-primary"></i>
                    Order Information
                </h3>
                
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600 dark:text-gray-400">Order Number:</span>
                        <span class="font-semibold text-gray-900 dark:text-white">#{{ order.id }}</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600 dark:text-gray-400">Table:</span>
                        <span class="font-semibold text-gray-900 dark:text-white">{{ order.table.number }}</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600 dark:text-gray-400">Order Time:</span>
                        <span class="font-semibold text-gray-900 dark:text-white">{{ order.created_at|date:"H:i" }}</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600 dark:text-gray-400">Date:</span>
                        <span class="font-semibold text-gray-900 dark:text-white">{{ order.created_at|date:"M d, Y" }}</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600 dark:text-gray-400">Estimated Time:</span>
                        <span class="font-semibold text-gray-900 dark:text-white">{{ order.estimated_time }} min</span>
                    </div>
                    
                    {% if order.estimated_ready_time %}
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600 dark:text-gray-400">Ready By:</span>
                        <span class="font-semibold text-gray-900 dark:text-white">{{ order.estimated_ready_time|date:"H:i" }}</span>
                    </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- Update Status -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    <i class="fas fa-edit mr-2 text-primary"></i>
                    Update Status
                </h3>
                
                <form method="POST" action="{% url 'restaurant:update_order_status' order.id %}">
                    {% csrf_token %}
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Order Status
                            </label>
                            <select name="status" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                                {% for value, label in order.STATUS_CHOICES %}
                                <option value="{{ value }}" {% if order.status == value %}selected{% endif %}>{{ label }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <button type="submit" class="w-full bg-primary text-white py-2 rounded-lg hover:bg-secondary transition-colors duration-200">
                            <i class="fas fa-save mr-2"></i>
                            Update Status
                        </button>
                    </div>
                </form>
            </div>
            
            <!-- Quick Actions -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    <i class="fas fa-bolt mr-2 text-primary"></i>
                    Quick Actions
                </h3>
                
                <div class="space-y-3">
                    {% if order.status == 'pending' %}
                    <form method="POST" action="{% url 'restaurant:update_order_status' order.id %}" class="w-full">
                        {% csrf_token %}
                        <input type="hidden" name="status" value="confirmed">
                        <button type="submit" class="w-full bg-blue-500 text-white py-2 rounded-lg hover:bg-blue-600 transition-colors duration-200">
                            <i class="fas fa-check mr-2"></i>
                            Confirm Order
                        </button>
                    </form>
                    {% endif %}
                    
                    {% if order.status == 'confirmed' %}
                    <form method="POST" action="{% url 'restaurant:update_order_status' order.id %}" class="w-full">
                        {% csrf_token %}
                        <input type="hidden" name="status" value="preparing">
                        <button type="submit" class="w-full bg-orange-500 text-white py-2 rounded-lg hover:bg-orange-600 transition-colors duration-200">
                            <i class="fas fa-fire mr-2"></i>
                            Start Preparing
                        </button>
                    </form>
                    {% endif %}
                    
                    {% if order.status == 'preparing' %}
                    <form method="POST" action="{% url 'restaurant:update_order_status' order.id %}" class="w-full">
                        {% csrf_token %}
                        <input type="hidden" name="status" value="ready">
                        <button type="submit" class="w-full bg-green-500 text-white py-2 rounded-lg hover:bg-green-600 transition-colors duration-200">
                            <i class="fas fa-bell mr-2"></i>
                            Mark as Ready
                        </button>
                    </form>
                    {% endif %}
                    
                    {% if order.status == 'ready' %}
                    <form method="POST" action="{% url 'restaurant:update_order_status' order.id %}" class="w-full">
                        {% csrf_token %}
                        <input type="hidden" name="status" value="served">
                        <button type="submit" class="w-full bg-gray-500 text-white py-2 rounded-lg hover:bg-gray-600 transition-colors duration-200">
                            <i class="fas fa-check-circle mr-2"></i>
                            Mark as Completed
                        </button>
                    </form>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Auto-refresh every 30 seconds
    setInterval(() => {
        location.reload();
    }, 30000);
</script>
{% endblock %}

