from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.core.paginator import Paginator
from django.db.models import Q, Sum, Count
from django.utils import timezone
from datetime import datetime, timedelta
import json
import qrcode
from io import BytesIO
import base64
import uuid

from .models import (
    Category, Product, Table, Order, OrderItem, 
    RestaurantSettings, AdminProfile
)
from .forms import (
    ProductForm, CategoryForm, TableForm, 
    RestaurantSettingsForm, AdminProfileForm
)
from .utils import save_invoice_pdf, create_invoice_response


def home(request):
    """
    Page d'accueil professionnelle de RestoQR
    """
    # Statistiques générales pour la page d'accueil
    context = {
        'total_restaurants': 1,  # Pour l'instant, un seul restaurant
        'total_orders': Order.objects.count(),
        'total_tables': Table.objects.count(),
        'total_products': Product.objects.count(),
    }
    return render(request, 'restaurant/home.html', context)


def login_redirect(request):
    """
    Redirection de /login/ vers /admin/login/ pour compatibilité
    """
    return redirect('restaurant:admin_login')


# Authentication Views
def admin_login(request):
    if request.user.is_authenticated:
        return redirect('restaurant:admin_dashboard')
    
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')
        
        user = authenticate(request, username=username, password=password)
        if user is not None and user.is_staff:
            login(request, user)
            return redirect('restaurant:admin_dashboard')
        else:
            messages.error(request, 'Invalid credentials or insufficient permissions.')
    
    return render(request, 'restaurant/admin/login.html')


@login_required
def admin_logout(request):
    logout(request)
    return redirect('restaurant:admin_login')


# Dashboard Views
@login_required
def admin_dashboard(request):
    # Get today's statistics
    today = timezone.now().date()
    yesterday = today - timedelta(days=1)
    today_orders = Order.objects.filter(created_at__date=today)
    yesterday_orders = Order.objects.filter(created_at__date=yesterday)
    
    # Basic stats
    today_revenue = today_orders.aggregate(Sum('total_amount'))['total_amount__sum'] or 0
    yesterday_revenue = yesterday_orders.aggregate(Sum('total_amount'))['total_amount__sum'] or 0
    
    # Calculate average order value
    avg_order_value = today_revenue / today_orders.count() if today_orders.count() > 0 else 0
    yesterday_avg = yesterday_revenue / yesterday_orders.count() if yesterday_orders.count() > 0 else 0
    avg_order_growth = ((avg_order_value - yesterday_avg) / yesterday_avg * 100) if yesterday_avg > 0 else 0
    
    # Completion rate
    completed_orders = today_orders.filter(status='served').count()
    completion_rate = (completed_orders / today_orders.count() * 100) if today_orders.count() > 0 else 0
    
    # Peak hour analysis
    from django.db.models.functions import Extract

    hourly_orders = (
        today_orders
        .annotate(hour=Extract('created_at', 'hour'))
        .values('hour')
        .annotate(count=Count('id'))
        .order_by('-count')
    )
    peak_hour = hourly_orders.first()['hour'] if hourly_orders.exists() else 12
    peak_hour_orders = hourly_orders.first()['count'] if hourly_orders.exists() else 0
    
    stats = {
        'today_orders_count': today_orders.count(),
        'today_revenue': today_revenue,
        'pending_orders': Order.objects.filter(status='pending').count(),
        'total_products': Product.objects.filter(is_available=True).count(),
        'total_tables': Table.objects.filter(is_active=True).count(),
        'avg_order_value': avg_order_value,
        'avg_order_growth': avg_order_growth,
        'completion_rate': completion_rate,
        'completed_orders': completed_orders,
        'peak_hour': peak_hour,
        'peak_hour_orders': peak_hour_orders,
    }
    
    # Recent orders
    recent_orders = Order.objects.select_related('table').prefetch_related('items__product')[:10]
    
    # Top products today
    top_products = (
        OrderItem.objects
        .filter(order__created_at__date=today)
        .values('product__name')
        .annotate(total_quantity=Sum('quantity'))
        .order_by('-total_quantity')[:5]
    )
    
    # Revenue data for the last 7 days
    revenue_data = []
    orders_data = []
    revenue_labels = []
    
    for i in range(6, -1, -1):
        date = today - timedelta(days=i)
        day_orders = Order.objects.filter(created_at__date=date)
        day_revenue = day_orders.aggregate(Sum('total_amount'))['total_amount__sum'] or 0
        
        revenue_data.append(float(day_revenue))
        orders_data.append(day_orders.count())
        revenue_labels.append(date.strftime('%m/%d'))
    
    # Table performance
    table_stats = (
        Table.objects
        .annotate(
            orders_count=Count('orders', filter=Q(orders__created_at__date=today)),
            revenue=Sum('orders__total_amount', filter=Q(orders__created_at__date=today))
        )
        .filter(is_active=True)
        .order_by('number')
    )
    
    # Convert to list and handle None values
    for table in table_stats:
        table.revenue = table.revenue or 0
    
    context = {
        'stats': stats,
        'recent_orders': recent_orders,
        'top_products': top_products,
        'revenue_data': revenue_data,
        'orders_data': orders_data,
        'revenue_labels': revenue_labels,
        'table_stats': table_stats,
    }
    
    return render(request, 'restaurant/admin/dashboard.html', context)


# Product Management Views
@login_required
def product_list(request):
    products = Product.objects.select_related('category').all()
    
    # Search functionality
    search_query = request.GET.get('search')
    if search_query:
        products = products.filter(
            Q(name__icontains=search_query) | 
            Q(description__icontains=search_query)
        )
    
    # Category filter
    category_id = request.GET.get('category')
    if category_id:
        products = products.filter(category_id=category_id)
    
    # Pagination
    paginator = Paginator(products, 12)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    categories = Category.objects.all()
    
    context = {
        'page_obj': page_obj,
        'categories': categories,
        'search_query': search_query,
        'selected_category': category_id,
    }
    
    return render(request, 'restaurant/admin/products/list.html', context)


@login_required
def product_add(request):
    if request.method == 'POST':
        form = ProductForm(request.POST, request.FILES)
        if form.is_valid():
            form.save()
            messages.success(request, 'Product added successfully!')
            return redirect('restaurant:product_list')
    else:
        form = ProductForm()
    
    return render(request, 'restaurant/admin/products/form.html', {
        'form': form,
        'title': 'Add Product'
    })


@login_required
def product_edit(request, pk):
    product = get_object_or_404(Product, pk=pk)
    
    if request.method == 'POST':
        form = ProductForm(request.POST, request.FILES, instance=product)
        if form.is_valid():
            form.save()
            messages.success(request, 'Product updated successfully!')
            return redirect('restaurant:product_list')
    else:
        form = ProductForm(instance=product)
    
    return render(request, 'restaurant/admin/products/form.html', {
        'form': form,
        'product': product,
        'title': 'Edit Product'
    })


@login_required
def product_delete(request, pk):
    product = get_object_or_404(Product, pk=pk)
    
    if request.method == 'POST':
        product.delete()
        messages.success(request, 'Product deleted successfully!')
        return redirect('restaurant:product_list')
    
    return render(request, 'restaurant/admin/products/delete.html', {
        'product': product
    })


# Category Management Views
@login_required
def category_list(request):
    categories = Category.objects.annotate(
        product_count=Count('products')
    ).all()
    
    return render(request, 'restaurant/admin/categories/list.html', {
        'categories': categories
    })


@login_required
def category_add(request):
    if request.method == 'POST':
        form = CategoryForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'Category added successfully!')
            return redirect('restaurant:category_list')
    else:
        form = CategoryForm()
    
    return render(request, 'restaurant/admin/categories/form.html', {
        'form': form,
        'title': 'Add Category'
    })


@login_required
def category_edit(request, pk):
    category = get_object_or_404(Category, pk=pk)
    
    if request.method == 'POST':
        form = CategoryForm(request.POST, instance=category)
        if form.is_valid():
            form.save()
            messages.success(request, 'Category updated successfully!')
            return redirect('restaurant:category_list')
    else:
        form = CategoryForm(instance=category)
    
    return render(request, 'restaurant/admin/categories/form.html', {
        'form': form,
        'category': category,
        'title': 'Edit Category'
    })


@login_required
def category_delete(request, pk):
    category = get_object_or_404(Category, pk=pk)
    
    if request.method == 'POST':
        category.delete()
        messages.success(request, 'Category deleted successfully!')
        return redirect('restaurant:category_list')
    
    return render(request, 'restaurant/admin/categories/delete.html', {
        'category': category
    })


# Table Management Views
@login_required
def table_list(request):
    tables = Table.objects.all().order_by('number')
    
    return render(request, 'restaurant/admin/tables/list.html', {
        'tables': tables
    })


@login_required
def table_add(request):
    if request.method == 'POST':
        form = TableForm(request.POST)
        if form.is_valid():
            table = form.save()
            # Generate QR code for the table
            table.qr_code = f"table_{table.number}_{table.id}"
            table.save()
            messages.success(request, 'Table added successfully!')
            return redirect('restaurant:table_list')
    else:
        form = TableForm()
    
    return render(request, 'restaurant/admin/tables/form.html', {
        'form': form,
        'title': 'Add Table'
    })


@login_required
def table_edit(request, pk):
    table = get_object_or_404(Table, pk=pk)
    
    if request.method == 'POST':
        form = TableForm(request.POST, instance=table)
        if form.is_valid():
            form.save()
            messages.success(request, 'Table updated successfully!')
            return redirect('restaurant:table_list')
    else:
        form = TableForm(instance=table)
    
    return render(request, 'restaurant/admin/tables/form.html', {
        'form': form,
        'table': table,
        'title': 'Edit Table'
    })


@login_required
def table_delete(request, pk):
    table = get_object_or_404(Table, pk=pk)
    
    if request.method == 'POST':
        table.delete()
        messages.success(request, 'Table deleted successfully!')
        return redirect('restaurant:table_list')
    
    return render(request, 'restaurant/admin/tables/delete.html', {
        'table': table
    })


@login_required
def generate_table_qr(request, pk):
    table = get_object_or_404(Table, pk=pk)
    
    # Generate QR code URL
    qr_url = request.build_absolute_uri(f'/qr/{table.id}/')
    
    # Create QR code
    qr = qrcode.QRCode(version=1, box_size=10, border=5)
    qr.add_data(qr_url)
    qr.make(fit=True)
    
    # Create QR code image
    img = qr.make_image(fill_color="black", back_color="white")
    
    # Convert to base64 for display
    buffer = BytesIO()
    img.save(buffer, format='PNG')
    img_str = base64.b64encode(buffer.getvalue()).decode()
    
    return render(request, 'restaurant/admin/tables/qr_code.html', {
        'table': table,
        'qr_code': img_str,
        'qr_url': qr_url
    })


# Order Management Views
@login_required
def order_list(request):
    """List all orders for admin"""
    orders = Order.objects.all().order_by('-created_at')
    
    # Filter by status if provided
    status_filter = request.GET.get('status')
    if status_filter:
        orders = orders.filter(status=status_filter)
    
    # Filter by date if provided
    date_filter = request.GET.get('date')
    if date_filter:
        orders = orders.filter(created_at__date=date_filter)
    
    context = {
        'orders': orders,
        'status_choices': Order.STATUS_CHOICES,
        'current_status': status_filter,
        'current_date': date_filter,
    }
    return render(request, 'restaurant/admin/orders/list.html', context)


@login_required
def order_detail(request, order_id):
    """View order details"""
    order = get_object_or_404(Order, id=order_id)
    return render(request, 'restaurant/admin/orders/detail.html', {'order': order})


@login_required
def update_order_status(request, order_id):
    """Update order status"""
    if request.method == 'POST':
        order = get_object_or_404(Order, id=order_id)
        new_status = request.POST.get('status')
        
        if new_status in dict(Order.STATUS_CHOICES):
            order.status = new_status
            order.save()
            messages.success(request, f'Order #{order.id} status updated to {order.get_status_display()}.')
        else:
            messages.error(request, 'Invalid status.')
    
    return redirect('restaurant:order_detail', order_id=order_id)


# Settings Views
@login_required
def restaurant_settings(request):
    settings_obj, created = RestaurantSettings.objects.get_or_create(
        defaults={'name': 'RestoQR Restaurant'}
    )
    
    if request.method == 'POST':
        form = RestaurantSettingsForm(request.POST, request.FILES, instance=settings_obj)
        if form.is_valid():
            form.save()
            messages.success(request, 'Settings updated successfully!')
            return redirect('restaurant:restaurant_settings')
    else:
        form = RestaurantSettingsForm(instance=settings_obj)
    
    return render(request, 'restaurant/admin/settings.html', {
        'form': form,
        'settings': settings_obj
    })


@login_required
def admin_profile(request):
    from django.contrib.auth import update_session_auth_hash
    from django.contrib.auth.forms import PasswordChangeForm
    
    if request.method == 'POST':
        action = request.POST.get('action')
        
        if action == 'change_password':
            # Handle password change
            current_password = request.POST.get('current_password')
            new_password1 = request.POST.get('new_password1')
            new_password2 = request.POST.get('new_password2')
            
            # Verify current password
            if not request.user.check_password(current_password):
                messages.error(request, 'Mot de passe actuel incorrect.')
            elif new_password1 != new_password2:
                messages.error(request, 'Les nouveaux mots de passe ne correspondent pas.')
            elif len(new_password1) < 8:
                messages.error(request, 'Le mot de passe doit contenir au moins 8 caractères.')
            else:
                # Change password
                request.user.set_password(new_password1)
                request.user.save()
                update_session_auth_hash(request, request.user)  # Keep user logged in
                messages.success(request, 'Mot de passe changé avec succès!')
                return redirect('restaurant:admin_profile')
        
        else:
            # Handle profile update
            first_name = request.POST.get('first_name', '')
            last_name = request.POST.get('last_name', '')
            username = request.POST.get('username', '')
            email = request.POST.get('email', '')
            
            # Update user information
            request.user.first_name = first_name
            request.user.last_name = last_name
            request.user.username = username
            request.user.email = email
            request.user.save()
            
            messages.success(request, 'Profil mis à jour avec succès!')
            return redirect('restaurant:admin_profile')
    
    return render(request, 'restaurant/admin/profile.html')


# PDF Invoice Generation
@login_required
def download_invoice(request, order_id):
    """Download PDF invoice for an order"""
    try:
        order = get_object_or_404(Order, id=order_id)
        save_invoice_pdf(order) # Save PDF to server for verification
        return create_invoice_response(order)
    except Order.DoesNotExist:
        return HttpResponse("Order not found.", status=404)


# API Views for Client Interface
@csrf_exempt
@require_http_methods(["POST"])
def api_place_order(request):
    """API endpoint to create a new order"""
    try:
        data = json.loads(request.body)
        table_id = data.get('table_id')
        items = data.get('items', [])
        customer_notes = data.get('notes', '')
        
        if not table_id or not items:
            return JsonResponse({
                'success': False,
                'error': 'Table ID and items are required'
            }, status=400)
        
        table = get_object_or_404(Table, id=table_id, is_active=True)
        
        # Create order
        order = Order.objects.create(
            table=table,
            customer_notes=customer_notes,
            status='pending'
        )
        
        # Create order items
        for item_data in items:
            product = get_object_or_404(Product, id=item_data["id"])
            quantity = int(item_data["quantity"])
            notes = item_data.get("notes", "")
            
            # Create and save the order item
            order_item = OrderItem.objects.create(
                order=order,
                product=product,
                quantity=quantity,
                notes=notes
            )
            # The save method will automatically set unit_price and subtotal
        
        # Refresh the order from database to get updated items
        order.refresh_from_db()
        
        # Update order total and estimated ready time AFTER all items are saved

        
        return JsonResponse({
            'success': True,
            'order_id': str(order.id),
            'total_amount': float(order.total_amount),
            'estimated_ready_time': order.estimated_ready_time.isoformat() if order.estimated_ready_time else None,
        })
    
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=400)


@csrf_exempt
def api_order_status(request, order_id):
    """API endpoint to get order status"""
    try:
        order = get_object_or_404(Order, id=order_id)
        
        return JsonResponse({
            'success': True,
            'order': {
                'id': str(order.id),
                'status': order.status,
                'total_amount': float(order.total_amount),
                'created_at': order.created_at.isoformat(),
                'estimated_ready_time': order.estimated_ready_time.isoformat() if order.estimated_ready_time else None,
                'table_number': order.table.number,
            }
        })
    
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=400)


# Client Interface Views (QR Code Access)
def client_menu(request, table_id):
    """Client menu view accessed via QR code"""
    table = get_object_or_404(Table, id=table_id, is_active=True)
    categories = Category.objects.prefetch_related('products').all()
    
    return render(request, 'restaurant/client/menu.html', {
        'table': table,
        'categories': categories,
    })


def client_thank_you(request, table_id):
    order_id = request.GET.get("order")
    order = get_object_or_404(Order, id=order_id)
    table = get_object_or_404(Table, id=table_id, is_active=True)
    return render(request, "restaurant/client/thank_you.html", {"table": table, "order": order})


def client_order_status(request, order_id):
    """Client order status view"""
    order = get_object_or_404(Order, id=order_id)
    
    return render(request, 'restaurant/client/order_status.html', {
        'order': order,
    })


def client_download_invoice(request, order_id):
    """Download PDF invoice for an order (client access)"""
    try:
        order = Order.objects.get(id=order_id)
        return create_invoice_response(order)
    except Order.DoesNotExist:
        return HttpResponse("Order not found.", status=404)


# Custom Error Views
def custom_404(request, exception):
    """Custom 404 error page"""
    return render(request, 'restaurant/errors/404.html', status=404)


def custom_500(request):
    """Custom 500 error page"""
    return render(request, 'restaurant/errors/500.html', status=500)

