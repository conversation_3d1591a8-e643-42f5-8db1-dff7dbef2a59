{% extends 'restaurant/admin/base.html' %}

{% block title %}Profil Admin - RestoQR{% endblock %}
{% block page_title %}Mon Profil{% endblock %}
{% block page_subtitle %}Gérez vos informations personnelles et préférences{% endblock %}

{% block admin_content %}
<div class="space-y-8">
    <!-- Profile Header -->
    <div class="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-700 rounded-2xl p-8 text-white relative overflow-hidden">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-10">
            <div class="absolute inset-0" style="background-image: radial-gradient(circle at 25% 25%, white 2px, transparent 2px); background-size: 24px 24px;"></div>
        </div>
        
        <div class="relative z-10 flex items-center space-x-6">
            <div class="w-24 h-24 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                <i class="fas fa-user text-4xl text-white/80"></i>
            </div>
            <div>
                <h1 class="text-3xl font-bold mb-2">{{ user.get_full_name|default:user.username }}</h1>
                <p class="text-blue-100 text-lg">Administrateur RestoQR</p>
                <div class="mt-2 flex items-center space-x-4 text-sm">
                    <div class="flex items-center">
                        <i class="fas fa-envelope mr-2"></i>
                        <span>{{ user.email|default:"Non défini" }}</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-calendar-alt mr-2"></i>
                        <span>Membre depuis {{ user.date_joined|date:"F Y" }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Profile Information -->
        <div class="lg:col-span-2 space-y-8">
            <!-- Personal Information -->
            <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-xl font-bold text-gray-900 dark:text-white flex items-center">
                        <i class="fas fa-user-edit mr-3 text-blue-500"></i>
                        Informations Personnelles
                    </h3>
                    <button onclick="toggleEditMode()" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-300">
                        <i class="fas fa-edit mr-2"></i>
                        Modifier
                    </button>
                </div>
                
                <form id="profile-form" method="post" class="space-y-6">
                    {% csrf_token %}
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Prénom
                            </label>
                            <input type="text" name="first_name" value="{{ user.first_name }}" 
                                   class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white disabled:bg-gray-100 dark:disabled:bg-gray-800" 
                                   disabled>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Nom
                            </label>
                            <input type="text" name="last_name" value="{{ user.last_name }}" 
                                   class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white disabled:bg-gray-100 dark:disabled:bg-gray-800" 
                                   disabled>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Nom d'utilisateur
                            </label>
                            <input type="text" name="username" value="{{ user.username }}" 
                                   class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white disabled:bg-gray-100 dark:disabled:bg-gray-800" 
                                   disabled>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Email
                            </label>
                            <input type="email" name="email" value="{{ user.email }}" 
                                   class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white disabled:bg-gray-100 dark:disabled:bg-gray-800" 
                                   disabled>
                        </div>
                    </div>
                    
                    <div class="hidden" id="save-buttons">
                        <div class="flex space-x-4">
                            <button type="submit" class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-300">
                                <i class="fas fa-save mr-2"></i>
                                Sauvegarder
                            </button>
                            <button type="button" onclick="cancelEdit()" class="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors duration-300">
                                <i class="fas fa-times mr-2"></i>
                                Annuler
                            </button>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Change Password -->
            <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
                    <i class="fas fa-lock mr-3 text-red-500"></i>
                    Changer le Mot de Passe
                </h3>
                
                <form method="post" action="{% url 'restaurant:admin_profile' %}" class="space-y-6">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="change_password">
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Mot de passe actuel
                        </label>
                        <div class="relative">
                            <input type="password" name="current_password" 
                                   class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white pr-12" 
                                   required>
                            <button type="button" onclick="togglePassword(this)" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Nouveau mot de passe
                        </label>
                        <div class="relative">
                            <input type="password" name="new_password1" 
                                   class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white pr-12" 
                                   required>
                            <button type="button" onclick="togglePassword(this)" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Confirmer le nouveau mot de passe
                        </label>
                        <div class="relative">
                            <input type="password" name="new_password2" 
                                   class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white pr-12" 
                                   required>
                            <button type="button" onclick="togglePassword(this)" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    
                    <button type="submit" class="px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-300">
                        <i class="fas fa-key mr-2"></i>
                        Changer le Mot de Passe
                    </button>
                </form>
            </div>
        </div>

        <!-- Profile Stats & Settings -->
        <div class="space-y-8">
            <!-- Account Stats -->
            <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
                    <i class="fas fa-chart-bar mr-3 text-green-500"></i>
                    Statistiques du Compte
                </h3>
                
                <div class="space-y-4">
                    <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-calendar-check text-blue-500 mr-3"></i>
                            <span class="text-sm text-gray-600 dark:text-gray-300">Dernière connexion</span>
                        </div>
                        <span class="text-sm font-medium text-gray-900 dark:text-white">
                            {{ user.last_login|date:"d/m/Y H:i"|default:"Jamais" }}
                        </span>
                    </div>
                    
                    <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-user-plus text-green-500 mr-3"></i>
                            <span class="text-sm text-gray-600 dark:text-gray-300">Membre depuis</span>
                        </div>
                        <span class="text-sm font-medium text-gray-900 dark:text-white">
                            {{ user.date_joined|date:"d/m/Y" }}
                        </span>
                    </div>
                    
                    <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-shield-alt text-purple-500 mr-3"></i>
                            <span class="text-sm text-gray-600 dark:text-gray-300">Statut</span>
                        </div>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300">
                            <i class="fas fa-circle mr-1 text-xs"></i>
                            {% if user.is_superuser %}Super Admin{% elif user.is_staff %}Admin{% else %}Utilisateur{% endif %}
                        </span>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
                    <i class="fas fa-bolt mr-3 text-yellow-500"></i>
                    Actions Rapides
                </h3>
                
                <div class="space-y-3">
                    <a href="{% url 'restaurant:admin_dashboard' %}" class="flex items-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors duration-300">
                        <i class="fas fa-tachometer-alt text-blue-600 dark:text-blue-400 mr-3"></i>
                        <span class="text-gray-900 dark:text-white">Retour au Dashboard</span>
                    </a>
                    
                    <a href="{% url 'restaurant:restaurant_settings' %}" class="flex items-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors duration-300">
                        <i class="fas fa-cog text-green-600 dark:text-green-400 mr-3"></i>
                        <span class="text-gray-900 dark:text-white">Paramètres Restaurant</span>
                    </a>
                    
                    <a href="{% url 'restaurant:admin_logout' %}" class="flex items-center p-3 bg-red-50 dark:bg-red-900/20 rounded-lg hover:bg-red-100 dark:hover:bg-red-900/30 transition-colors duration-300">
                        <i class="fas fa-sign-out-alt text-red-600 dark:text-red-400 mr-3"></i>
                        <span class="text-gray-900 dark:text-white">Se Déconnecter</span>
                    </a>
                </div>
            </div>

            <!-- Security Tips -->
            <div class="bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 rounded-2xl p-6 border border-yellow-200 dark:border-yellow-700">
                <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4 flex items-center">
                    <i class="fas fa-shield-alt mr-3 text-yellow-600 dark:text-yellow-400"></i>
                    Conseils de Sécurité
                </h3>
                
                <ul class="space-y-2 text-sm text-gray-700 dark:text-gray-300">
                    <li class="flex items-start">
                        <i class="fas fa-check text-green-500 mr-2 mt-1 text-xs"></i>
                        <span>Utilisez un mot de passe fort avec au moins 8 caractères</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check text-green-500 mr-2 mt-1 text-xs"></i>
                        <span>Changez votre mot de passe régulièrement</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check text-green-500 mr-2 mt-1 text-xs"></i>
                        <span>Ne partagez jamais vos identifiants</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check text-green-500 mr-2 mt-1 text-xs"></i>
                        <span>Déconnectez-vous après chaque session</span>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript -->
<script>
    function toggleEditMode() {
        const form = document.getElementById('profile-form');
        const inputs = form.querySelectorAll('input[type="text"], input[type="email"]');
        const saveButtons = document.getElementById('save-buttons');
        
        inputs.forEach(input => {
            input.disabled = !input.disabled;
            if (!input.disabled) {
                input.focus();
            }
        });
        
        saveButtons.classList.toggle('hidden');
    }
    
    function cancelEdit() {
        const form = document.getElementById('profile-form');
        const inputs = form.querySelectorAll('input[type="text"], input[type="email"]');
        const saveButtons = document.getElementById('save-buttons');
        
        inputs.forEach(input => {
            input.disabled = true;
        });
        
        saveButtons.classList.add('hidden');
        
        // Reset form values
        form.reset();
    }
    
    function togglePassword(button) {
        const input = button.parentElement.querySelector('input');
        const icon = button.querySelector('i');
        
        if (input.type === 'password') {
            input.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            input.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    }
    
    // Form validation
    document.addEventListener('DOMContentLoaded', function() {
        const passwordForm = document.querySelector('form[action*="admin_profile"]');
        if (passwordForm) {
            passwordForm.addEventListener('submit', function(e) {
                const newPassword1 = this.querySelector('input[name="new_password1"]').value;
                const newPassword2 = this.querySelector('input[name="new_password2"]').value;
                
                if (newPassword1 !== newPassword2) {
                    e.preventDefault();
                    alert('Les nouveaux mots de passe ne correspondent pas.');
                    return false;
                }
                
                if (newPassword1.length < 8) {
                    e.preventDefault();
                    alert('Le mot de passe doit contenir au moins 8 caractères.');
                    return false;
                }
            });
        }
    });
</script>
{% endblock %}