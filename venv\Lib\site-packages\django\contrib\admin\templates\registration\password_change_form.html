{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block title %}{% if form.errors %}{% translate "Error:" %} {% endif %}{{ block.super }}{% endblock %}
{% block extrastyle %}{{ block.super }}<link rel="stylesheet" href="{% static "admin/css/forms.css" %}">{% endblock %}
{% block userlinks %}
  {% url 'django-admindocs-docroot' as docsroot %}{% if docsroot %}<a href="{{ docsroot }}">{% translate 'Documentation' %}</a> / {% endif %} {% translate 'Change password' %} /
  <form id="logout-form" method="post" action="{% url 'admin:logout' %}">
    {% csrf_token %}
    <button type="submit">{% translate 'Log out' %}</button>
  </form>
  {% include "admin/color_theme_toggle.html" %}
{% endblock %}
{% block breadcrumbs %}
<div class="breadcrumbs">
<a href="{% url 'admin:index' %}">{% translate 'Home' %}</a>
&rsaquo; {% translate 'Password change' %}
</div>
{% endblock %}

{% block content %}<div id="content-main">

<form method="post">{% csrf_token %}
<div>
{% if form.errors %}
    <p class="errornote">
    {% blocktranslate count counter=form.errors.items|length %}Please correct the error below.{% plural %}Please correct the errors below.{% endblocktranslate %}
    </p>
{% endif %}


<p>{% translate 'Please enter your old password, for security’s sake, and then enter your new password twice so we can verify you typed it in correctly.' %}</p>

<fieldset class="module aligned wide">

<div class="form-row">
    {{ form.old_password.errors }}
    <div class="flex-container">{{ form.old_password.label_tag }} {{ form.old_password }}</div>
</div>

<div class="form-row">
    {{ form.new_password1.errors }}
    <div class="flex-container">{{ form.new_password1.label_tag }} {{ form.new_password1 }}</div>
    {% if form.new_password1.help_text %}
    <div class="help"{% if form.new_password1.id_for_label %} id="{{ form.new_password1.id_for_label }}_helptext"{% endif %}>{{ form.new_password1.help_text|safe }}</div>
    {% endif %}
</div>

<div class="form-row">
    {{ form.new_password2.errors }}
    <div class="flex-container">{{ form.new_password2.label_tag }} {{ form.new_password2 }}</div>
    {% if form.new_password2.help_text %}
    <div class="help"{% if form.new_password2.id_for_label %} id="{{ form.new_password2.id_for_label }}_helptext"{% endif %}>{{ form.new_password2.help_text|safe }}</div>
    {% endif %}
</div>

</fieldset>

<div class="submit-row">
    <input type="submit" value="{% translate 'Change my password' %}" class="default">
</div>

</div>
</form></div>

{% endblock %}
