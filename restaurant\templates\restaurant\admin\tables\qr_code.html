{% extends 'restaurant/admin/base.html' %}

{% block title %}QR Code - Table {{ table.number }} - RestoQR Admin{% endblock %}
{% block page_title %}QR Code - Table {{ table.number }}{% endblock %}

{% block admin_content %}
<div class="max-w-4xl mx-auto space-y-6 fade-in">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <a href="{% url 'restaurant:table_list' %}" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                <i class="fas fa-arrow-left text-xl"></i>
            </a>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">QR Code for Table {{ table.number }}</h1>
        </div>
    </div>
    
    <!-- QR Code Display -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- QR Code -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-8 text-center">
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">
                <i class="fas fa-qrcode mr-2 text-primary"></i>
                Scan to Access Menu
            </h3>
            
            <div class="bg-white p-8 rounded-lg inline-block shadow-lg">
                <img src="data:image/png;base64,{{ qr_code }}" alt="QR Code for Table {{ table.number }}" class="w-64 h-64 mx-auto">
            </div>
            
            <div class="mt-6 space-y-4">
                <p class="text-gray-600 dark:text-gray-400">
                    Customers can scan this QR code to access the menu and place orders for Table {{ table.number }}.
                </p>
                
                <div class="flex space-x-4 justify-center">
                    <button onclick="downloadQR()" class="px-6 py-2 bg-primary text-white rounded-lg hover:bg-secondary transition-colors duration-200">
                        <i class="fas fa-download mr-2"></i>
                        Download QR Code
                    </button>
                    
                    <button onclick="printQR()" class="px-6 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors duration-200">
                        <i class="fas fa-print mr-2"></i>
                        Print QR Code
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Table Information -->
        <div class="space-y-6">
            <!-- Table Details -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    <i class="fas fa-info-circle mr-2 text-primary"></i>
                    Table Information
                </h3>
                
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600 dark:text-gray-400">Table Number:</span>
                        <span class="font-semibold text-gray-900 dark:text-white">{{ table.number }}</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600 dark:text-gray-400">Capacity:</span>
                        <span class="font-semibold text-gray-900 dark:text-white">{{ table.capacity }} people</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600 dark:text-gray-400">Status:</span>
                        {% if table.is_active %}
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                <i class="fas fa-check-circle mr-1"></i>
                                Active
                            </span>
                        {% else %}
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                <i class="fas fa-times-circle mr-1"></i>
                                Inactive
                            </span>
                        {% endif %}
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600 dark:text-gray-400">QR Code URL:</span>
                        <a href="{{ qr_url }}" target="_blank" class="text-primary hover:text-secondary text-sm font-medium truncate max-w-xs">
                            {{ qr_url }}
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Instructions -->
            <div class="bg-blue-50 dark:bg-blue-900 rounded-xl p-6">
                <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-200 mb-4">
                    <i class="fas fa-lightbulb mr-2"></i>
                    How to Use
                </h3>
                
                <ol class="space-y-2 text-blue-800 dark:text-blue-300">
                    <li class="flex items-start">
                        <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">1</span>
                        Print or display this QR code at Table {{ table.number }}
                    </li>
                    <li class="flex items-start">
                        <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">2</span>
                        Customers scan the QR code with their phone camera
                    </li>
                    <li class="flex items-start">
                        <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">3</span>
                        They can browse the menu and place orders directly
                    </li>
                    <li class="flex items-start">
                        <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">4</span>
                        Orders appear in your admin dashboard for processing
                    </li>
                </ol>
            </div>
            
            <!-- Quick Actions -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    <i class="fas fa-bolt mr-2 text-primary"></i>
                    Quick Actions
                </h3>
                
                <div class="space-y-3">
                    <a href="{% url 'restaurant:table_edit' table.pk %}" class="w-full bg-blue-500 text-white text-center py-2 rounded-lg hover:bg-blue-600 transition-colors duration-200 block">
                        <i class="fas fa-edit mr-2"></i>
                        Edit Table Settings
                    </a>
                    
                    <a href="{{ qr_url }}" target="_blank" class="w-full bg-green-500 text-white text-center py-2 rounded-lg hover:bg-green-600 transition-colors duration-200 block">
                        <i class="fas fa-external-link-alt mr-2"></i>
                        Preview Customer View
                    </a>
                    
                    <a href="{% url 'restaurant:table_list' %}" class="w-full bg-gray-500 text-white text-center py-2 rounded-lg hover:bg-gray-600 transition-colors duration-200 block">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back to Tables
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function downloadQR() {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const img = new Image();
        
        img.onload = function() {
            canvas.width = img.width;
            canvas.height = img.height;
            ctx.drawImage(img, 0, 0);
            
            const link = document.createElement('a');
            link.download = 'table-{{ table.number }}-qr-code.png';
            link.href = canvas.toDataURL();
            link.click();
        };
        
        img.src = 'data:image/png;base64,{{ qr_code }}';
    }
    
    function printQR() {
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <html>
                <head>
                    <title>QR Code - Table {{ table.number }}</title>
                    <style>
                        body { 
                            font-family: Arial, sans-serif; 
                            text-align: center; 
                            padding: 20px; 
                        }
                        .qr-container { 
                            border: 2px solid #000; 
                            padding: 20px; 
                            display: inline-block; 
                            margin: 20px;
                        }
                        h1 { 
                            margin-bottom: 10px; 
                        }
                        .instructions { 
                            margin-top: 20px; 
                            font-size: 14px; 
                        }
                    </style>
                </head>
                <body>
                    <h1>Table {{ table.number }}</h1>
                    <div class="qr-container">
                        <img src="data:image/png;base64,{{ qr_code }}" alt="QR Code" style="width: 200px; height: 200px;">
                    </div>
                    <div class="instructions">
                        <p><strong>Scan to view menu and place orders</strong></p>
                        <p>{{ qr_url }}</p>
                    </div>
                </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();
    }
</script>
{% endblock %}

