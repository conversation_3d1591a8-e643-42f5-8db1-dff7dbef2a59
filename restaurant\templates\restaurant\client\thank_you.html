{% extends 'restaurant/base.html' %}

{% block title %}Order Confirmed - Table {{ table.number }} - RestoQR{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center px-4">
    <div class="max-w-md w-full">
        <!-- Success Animation -->
        <div class="text-center mb-8">
            <div class="w-20 h-20 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4 animate-bounce">
                <i class="fas fa-check text-white text-3xl"></i>
            </div>
                 <h1 class="text-3xl font-bold text-gray-900 dark:text-light_text mb-2">Order Confirmed!</h1>
            <p class="text-gray-600 dark:text-gray-400">Thank you for your order</p>
        </div>
        
        <!-- Order Details Card -->
        <div class="bg-white dark:bg-dark_card rounded-xl shadow-lg p-6 mb-6">
            <div class="text-center mb-6">
                <div class="flex items-center justify-center space-x-3 mb-4">
                    <div class="w-12 h-12 bg-primary rounded-full flex items-center justify-center">
                        <i class="fas fa-utensils text-white"></i>
                    </div>
                    <div>
                        <h2 class="text-xl font-bold text-gray-900 dark:text-white">RestoQR</h2>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Table {{ table.number }}</p>
                    </div>
                </div>
            </div>
            
            <!-- Order Information -->
            <div class="space-y-4">
                <div class="flex items-center justify-between py-2 border-b border-gray-200 dark:border-gray-700">
                    <span class="text-gray-600 dark:text-gray-400">Order Number:</span>
                    <span class="font-semibold text-gray-900 dark:text-white">#{{ order.id }}</span>
                </div>
                
                <div class="flex items-center justify-between py-2 border-b border-gray-200 dark:border-gray-700">
                    <span class="text-gray-600 dark:text-gray-400">Table:</span>
                    <span class="font-semibold text-gray-900 dark:text-white">{{ table.number }}</span>
                </div>
                
                <div class="flex items-center justify-between py-2 border-b border-gray-200 dark:border-gray-700">
                    <span class="text-gray-600 dark:text-gray-400">Order Time:</span>
                    <span class="font-semibold text-gray-900 dark:text-white">{{ order.created_at|date:"H:i" }}</span>
                </div>
                
                <div class="flex items-center justify-between py-2 border-b border-gray-200 dark:border-gray-700">
                    <span class="text-gray-600 dark:text-gray-400">Total Amount:</span>
                    <span class="font-bold text-primary text-lg">{{ order.total_amount }} dh</span>
                </div>
                
                <div class="flex items-center justify-between py-2">
                    <span class="text-gray-600 dark:text-gray-400">Status:</span>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                        <i class="fas fa-clock mr-1"></i>
                        {{ order.get_status_display }}
                    </span>
                </div>
            </div>
        </div>
        
        <!-- Order Items -->
        <div class="bg-white dark:bg-dark_card rounded-xl shadow-lg p-6 mb-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                <i class="fas fa-list mr-2 text-primary"></i>
                Order Items
            </h3>
            
            <div class="space-y-3">
                {% for item in order.items.all %}
                <div class="flex items-center justify-between">
                    <div class="flex-1">
                        <h4 class="font-medium text-gray-900 dark:text-white">{{ item.product.name }}</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            {{ item.price }} dh × {{ item.quantity }}
                        </p>
                    </div>
                    <span class="font-semibold text-gray-900 dark:text-white">
                        {{ item.subtotal }} dh
                    </span>
                </div>
                {% endfor %}
                
                {% if order.notes %}
                <div class="pt-3 border-t border-gray-200 dark:border-gray-700">
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                        <i class="fas fa-sticky-note mr-1"></i>
                        <strong>Special Instructions:</strong> {{ order.notes }}
                    </p>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Estimated Time -->
        <div class="bg-blue-50 dark:bg-blue-900 rounded-xl p-6 mb-6">
            <div class="text-center">
                <i class="fas fa-clock text-blue-500 text-2xl mb-2"></i>
                <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-200 mb-2">
                    Estimated Preparation Time
                </h3>
                <p class="text-2xl font-bold text-blue-600 dark:text-blue-300 mb-2">
                    {{ order.estimated_time }} minutes
                </p>
                <p class="text-sm text-blue-800 dark:text-blue-400">
                    Your order will be ready around {{ order.estimated_ready_time|date:"H:i" }}
                </p>
            </div>
        </div>
        
        <!-- What's Next -->
        <div class="bg-white dark:bg-dark_card rounded-xl shadow-lg p-6 mb-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                <i class="fas fa-info-circle mr-2 text-primary"></i>
                What's Next?
            </h3>
            
            <div class="space-y-3">
                <div class="flex items-start space-x-3">
                    <div class="w-6 h-6 bg-primary rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                        <span class="text-white text-sm font-bold">1</span>
                    </div>
                    <p class="text-gray-600 dark:text-gray-400">
                        Your order has been sent to our kitchen
                    </p>
                </div>
                
                <div class="flex items-start space-x-3">
                    <div class="w-6 h-6 bg-primary rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                        <span class="text-white text-sm font-bold">2</span>
                    </div>
                    <p class="text-gray-600 dark:text-gray-400">
                        Our chefs are preparing your delicious meal
                    </p>
                </div>
                
                <div class="flex items-start space-x-3">
                    <div class="w-6 h-6 bg-primary rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                        <span class="text-white text-sm font-bold">3</span>
                    </div>
                    <p class="text-gray-600 dark:text-gray-400">
                        Your order will be served to Table {{ table.number }}
                    </p>
                </div>
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="space-y-3">
            <a href="{% url 'restaurant:client_order_status' order.id %}" 
               class="w-full bg-primary text-white text-center py-3 rounded-lg hover:bg-secondary transition-colors duration-200 font-semibold block">
                <i class="fas fa-eye mr-2"></i>
                Track Order Status
            </a>
            
            <a href="{% url 'restaurant:client_menu' table_id=table.id %}" 
               class="w-full bg-gray-500 text-white text-center py-3 rounded-lg hover:bg-gray-600 transition-colors duration-200 font-semibold block">
                <i class="fas fa-plus mr-2"></i>
                Order More Items
            </a>
            
            <button onclick="downloadInvoice()" 
                    class="w-full bg-green-500 text-white text-center py-3 rounded-lg hover:bg-green-600 transition-colors duration-200 font-semibold">
                <i class="fas fa-download mr-2"></i>
                Download Invoice (PDF)
            </button>
        </div>
        
        <!-- Footer -->
        <div class="text-center mt-8">
            <p class="text-sm text-gray-500 dark:text-gray-400">
                Thank you for dining with us!
            </p>
            <p class="text-xs text-gray-400 dark:text-gray-500 mt-1">
                Need help? Ask our staff for assistance.
            </p>
        </div>
    </div>
</div>

<script>
    function downloadInvoice() {
        // Create a link to download the PDF invoice
        const link = document.createElement('a');
        link.href = '{% url "restaurant:download_invoice" order.id %}';
        link.download = 'invoice-{{ order.id }}.pdf';
        link.click();
    }
    
    // Auto-refresh order status every 30 seconds
    setInterval(() => {
        fetch(`/api/order-status/{{ order.id }}/`)
            .then(response => response.json())
            .then(data => {
                if (data.status !== '{{ order.status }}') {
                    location.reload();
                }
            })
            .catch(error => console.log('Status check failed:', error));
    }, 30000);
</script>
{% endblock %}

