{% extends 'restaurant/admin/base.html' %}

{% block title %}{% if form.instance.pk %}Edit Table{% else %}Add Table{% endif %} - RestoQR Admin{% endblock %}
{% block page_title %}{% if form.instance.pk %}Edit Table{% else %}Add New Table{% endif %}{% endblock %}

{% block admin_content %}
<div class="space-y-6 fade-in">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <a href="{% url 'restaurant:table_list' %}" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                <i class="fas fa-arrow-left text-xl"></i>
            </a>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                {% if form.instance.pk %}Edit Table{% else %}Add New Table{% endif %}
            </h1>
        </div>
    </div>
    
    <!-- Form -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
        <form method="post" class="space-y-6">
            {% csrf_token %}
            
            <!-- Table Number -->
            <div>
                <label for="{{ form.number.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Table Number *
                </label>
                {{ form.number }}
                {% if form.number.errors %}
                    <div class="mt-1 text-sm text-red-600">
                        {{ form.number.errors.0 }}
                    </div>
                {% endif %}
            </div>
            
            <!-- Capacity -->
            <div>
                <label for="{{ form.capacity.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Capacity *
                </label>
                {{ form.capacity }}
                {% if form.capacity.errors %}
                    <div class="mt-1 text-sm text-red-600">
                        {{ form.capacity.errors.0 }}
                    </div>
                {% endif %}
            </div>
            
            <!-- Is Active -->
            <div class="flex items-center">
                {{ form.is_active }}
                <label for="{{ form.is_active.id_for_label }}" class="ml-2 block text-sm text-gray-900 dark:text-gray-300">
                    Available for use
                </label>
                {% if form.is_active.errors %}
                    <div class="mt-1 text-sm text-red-600">
                        {{ form.is_active.errors.0 }}
                    </div>
                {% endif %}
            </div>
            
            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200 dark:border-gray-700">
                <a href="{% url 'restaurant:table_list' %}" class="btn-secondary">
                    Cancel
                </a>
                <button type="submit" class="btn-primary">
                    <i class="fas fa-save mr-2"></i>
                    {% if form.instance.pk %}Update Table{% else %}Create Table{% endif %}
                </button>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus on the first form field
    const firstField = document.querySelector('input[type="number"]');
    if (firstField) {
        firstField.focus();
    }
});
</script>
{% endblock %}

