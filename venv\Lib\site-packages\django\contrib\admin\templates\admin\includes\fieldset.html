<fieldset class="module aligned {{ fieldset.classes }}"{% if fieldset.name %} aria-labelledby="{{ prefix }}-{{ id_prefix}}-{{ id_suffix }}-heading"{% endif %}>
    {% if fieldset.name %}
        {% if fieldset.is_collapsible %}<details><summary>{% endif %}
        <h{{ heading_level|default:2 }} id="{{ prefix }}-{{ id_prefix}}-{{ id_suffix }}-heading" class="fieldset-heading">{{ fieldset.name }}</h{{ heading_level|default:2 }}>
        {% if fieldset.is_collapsible %}</summary>{% endif %}
    {% endif %}
    {% if fieldset.description %}
        <div class="description">{{ fieldset.description|safe }}</div>
    {% endif %}
    {% for line in fieldset %}
        <div class="form-row{% if line.fields|length == 1 and line.errors %} errors{% endif %}{% if not line.has_visible_field %} hidden{% endif %}{% for field in line %}{% if field.field.name %} field-{{ field.field.name }}{% endif %}{% endfor %}">
            {% if line.fields|length == 1 %}{{ line.errors }}{% else %}<div class="flex-container form-multiline">{% endif %}
            {% for field in line %}
                <div>
                    {% if not line.fields|length == 1 and not field.is_readonly %}{{ field.errors }}{% endif %}
                        <div class="flex-container{% if not line.fields|length == 1 %} fieldBox{% if field.field.name %} field-{{ field.field.name }}{% endif %}{% if not field.is_readonly and field.errors %} errors{% endif %}{% if field.field.is_hidden %} hidden{% endif %}{% endif %}{% if field.is_checkbox %} checkbox-row{% endif %}">
                            {% if field.is_checkbox %}
                                {{ field.field }}{{ field.label_tag }}
                            {% else %}
                                {{ field.label_tag }}
                                {% if field.is_readonly %}
                                    <div class="readonly">{{ field.contents }}</div>
                                {% else %}
                                    {{ field.field }}
                                {% endif %}
                            {% endif %}
                        </div>
                    {% if field.field.help_text %}
                        <div class="help{% if field.field.is_hidden %} hidden{% endif %}"{% if field.field.id_for_label %} id="{{ field.field.id_for_label }}_helptext"{% endif %}>
                            <div>{{ field.field.help_text|safe }}</div>
                        </div>
                    {% endif %}
                </div>
            {% endfor %}
            {% if not line.fields|length == 1 %}</div>{% endif %}
        </div>
    {% endfor %}
    {% if fieldset.name and fieldset.is_collapsible %}</details>{% endif %}
</fieldset>
