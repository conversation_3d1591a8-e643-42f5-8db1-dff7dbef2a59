{% extends 'restaurant/admin/base.html' %}

{% block title %}Dashboard - RestoQR Admin{% endblock %}
{% block page_title %}Dashboard{% endblock %}
{% block page_subtitle %}Vue d'ensemble de votre restaurant en temps réel{% endblock %}

{% block admin_content %}
<div class="space-y-8">
    <!-- Welcome Section -->
    <div class="relative overflow-hidden bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-700 rounded-2xl p-8 text-white">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-10">
            <div class="absolute top-0 left-0 w-full h-full" style="background-image: radial-gradient(circle at 25% 25%, white 2px, transparent 2px); background-size: 24px 24px;"></div>
        </div>
        
        <div class="relative z-10 flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold mb-2">Bienvenue, {{ user.get_full_name|default:user.username }} ! 👋</h1>
                <p class="text-blue-100 text-lg">Voici un aperçu de votre restaurant aujourd'hui</p>
                <div class="mt-4 flex items-center space-x-6 text-sm">
                    <div class="flex items-center">
                        <i class="fas fa-calendar-day mr-2"></i>
                        <span>{{ "now"|date:"l d F Y" }}</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-clock mr-2"></i>
                        <span id="currentTime">{{ "now"|date:"H:i" }}</span>
                    </div>
                </div>
            </div>
            <div class="hidden lg:block">
                <div class="w-32 h-32 bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm">
                    <i class="fas fa-chart-line text-6xl text-white/80"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Key Metrics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Today's Orders -->
        <div class="group relative overflow-hidden bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
            <div class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full -mr-10 -mt-10 opacity-20 group-hover:opacity-30 transition-opacity duration-300"></div>
            <div class="relative z-10">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                        <i class="fas fa-shopping-cart text-white text-lg"></i>
                    </div>
                    <div class="text-right">
                        <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.today_orders_count }}</div>
                        <div class="text-xs text-green-600 dark:text-green-400 font-medium">
                            <i class="fas fa-arrow-up mr-1"></i>+12%
                        </div>
                    </div>
                </div>
                <h3 class="text-gray-600 dark:text-gray-300 font-medium">Commandes Aujourd'hui</h3>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Comparé à hier</p>
            </div>
        </div>

        <!-- Today's Revenue -->
        <div class="group relative overflow-hidden bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
            <div class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-green-400 to-green-600 rounded-full -mr-10 -mt-10 opacity-20 group-hover:opacity-30 transition-opacity duration-300"></div>
            <div class="relative z-10">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center">
                        <i class="fas fa-coins text-white text-lg"></i>
                    </div>
                    <div class="text-right">
                        <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.today_revenue|floatformat:0 }}</div>
                        <div class="text-xs text-green-600 dark:text-green-400 font-medium">
                            <i class="fas fa-arrow-up mr-1"></i>+8%
                        </div>
                    </div>
                </div>
                <h3 class="text-gray-600 dark:text-gray-300 font-medium">Revenus (DH)</h3>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Objectif: 2000 DH</p>
            </div>
        </div>

        <!-- Pending Orders -->
        <div class="group relative overflow-hidden bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
            <div class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-amber-400 to-orange-600 rounded-full -mr-10 -mt-10 opacity-20 group-hover:opacity-30 transition-opacity duration-300"></div>
            <div class="relative z-10">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-12 h-12 bg-gradient-to-br from-amber-500 to-orange-600 rounded-xl flex items-center justify-center">
                        <i class="fas fa-clock text-white text-lg"></i>
                    </div>
                    <div class="text-right">
                        <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.pending_orders }}</div>
                        {% if stats.pending_orders > 0 %}
                        <div class="text-xs text-red-600 dark:text-red-400 font-medium animate-pulse">
                            <i class="fas fa-exclamation-triangle mr-1"></i>Urgent
                        </div>
                        {% else %}
                        <div class="text-xs text-green-600 dark:text-green-400 font-medium">
                            <i class="fas fa-check mr-1"></i>À jour
                        </div>
                        {% endif %}
                    </div>
                </div>
                <h3 class="text-gray-600 dark:text-gray-300 font-medium">En Attente</h3>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Commandes à traiter</p>
            </div>
        </div>

        <!-- Active Tables -->
        <div class="group relative overflow-hidden bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
            <div class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full -mr-10 -mt-10 opacity-20 group-hover:opacity-30 transition-opacity duration-300"></div>
            <div class="relative z-10">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center">
                        <i class="fas fa-chair text-white text-lg"></i>
                    </div>
                    <div class="text-right">
                        <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.total_tables }}</div>
                        <div class="text-xs text-blue-600 dark:text-blue-400 font-medium">
                            <i class="fas fa-qrcode mr-1"></i>QR Ready
                        </div>
                    </div>
                </div>
                <h3 class="text-gray-600 dark:text-gray-300 font-medium">Tables Actives</h3>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Avec QR codes</p>
            </div>
        </div>
    </div>

    <!-- Advanced Analytics -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Performance Metrics -->
        <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
            <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-6 flex items-center">
                <i class="fas fa-chart-line mr-3 text-blue-500"></i>
                Performances
            </h3>
            
            <div class="space-y-6">
                <!-- Average Order Value -->
                <div>
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm text-gray-600 dark:text-gray-300">Panier Moyen</span>
                        <span class="text-lg font-bold text-gray-900 dark:text-white">{{ stats.avg_order_value|floatformat:0 }} DH</span>
                    </div>
                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div class="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full" style="width: 75%"></div>
                    </div>
                    <div class="flex items-center mt-1">
                        <i class="fas fa-arrow-up text-green-500 text-xs mr-1"></i>
                        <span class="text-xs text-green-600 dark:text-green-400">+{{ stats.avg_order_growth|floatformat:1 }}% vs hier</span>
                    </div>
                </div>

                <!-- Completion Rate -->
                <div>
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm text-gray-600 dark:text-gray-300">Taux de Réussite</span>
                        <span class="text-lg font-bold text-gray-900 dark:text-white">{{ stats.completion_rate|floatformat:1 }}%</span>
                    </div>
                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div class="bg-gradient-to-r from-green-500 to-emerald-600 h-2 rounded-full" style="width: {{ stats.completion_rate }}%"></div>
                    </div>
                    <div class="flex items-center mt-1">
                        <i class="fas fa-check-circle text-green-500 text-xs mr-1"></i>
                        <span class="text-xs text-gray-500 dark:text-gray-400">{{ stats.completed_orders }} commandes terminées</span>
                    </div>
                </div>

                <!-- Peak Hour -->
                <div>
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm text-gray-600 dark:text-gray-300">Heure de Pointe</span>
                        <span class="text-lg font-bold text-gray-900 dark:text-white">{{ stats.peak_hour }}:00</span>
                    </div>
                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div class="bg-gradient-to-r from-orange-500 to-red-600 h-2 rounded-full" style="width: 60%"></div>
                    </div>
                    <div class="flex items-center mt-1">
                        <i class="fas fa-fire text-orange-500 text-xs mr-1"></i>
                        <span class="text-xs text-gray-500 dark:text-gray-400">{{ stats.peak_hour_orders }} commandes</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Orders -->
        <div class="lg:col-span-2 bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-bold text-gray-900 dark:text-white flex items-center">
                    <i class="fas fa-receipt mr-3 text-green-500"></i>
                    Commandes Récentes
                </h3>
                <a href="{% url 'restaurant:order_list' %}" class="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 text-sm font-medium flex items-center">
                    Voir tout <i class="fas fa-arrow-right ml-1"></i>
                </a>
            </div>
            
            <div class="space-y-3 max-h-80 overflow-y-auto">
                {% for order in recent_orders %}
                <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
                    <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center text-white font-bold">
                            {{ order.table.number }}
                        </div>
                        <div>
                            <div class="font-semibold text-gray-900 dark:text-white">
                                Commande #{{ order.id|slice:":8" }}...
                            </div>
                            <div class="text-sm text-gray-500 dark:text-gray-400 flex items-center">
                                <i class="fas fa-clock mr-1"></i>
                                {{ order.created_at|timesince }} ago
                            </div>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="font-bold text-gray-900 dark:text-white">{{ order.total_amount|floatformat:0 }} DH</div>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium
                            {% if order.status == 'pending' %}bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300
                            {% elif order.status == 'preparing' %}bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300
                            {% elif order.status == 'ready' %}bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300
                            {% elif order.status == 'served' %}bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300
                            {% else %}bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300{% endif %}">
                            <i class="fas fa-circle mr-1 text-xs"></i>
                            {{ order.get_status_display }}
                        </span>
                    </div>
                </div>
                {% empty %}
                <div class="text-center py-12">
                    <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-receipt text-gray-400 text-2xl"></i>
                    </div>
                    <h4 class="text-gray-500 dark:text-gray-400 font-medium">Aucune commande récente</h4>
                    <p class="text-gray-400 dark:text-gray-500 text-sm mt-1">Les nouvelles commandes apparaîtront ici</p>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Revenue Chart -->
        <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-bold text-gray-900 dark:text-white flex items-center">
                    <i class="fas fa-chart-area mr-3 text-purple-500"></i>
                    Évolution des Revenus
                </h3>
                <div class="flex items-center space-x-2">
                    <div class="flex items-center text-sm text-gray-600 dark:text-gray-300">
                        <div class="w-3 h-3 bg-purple-500 rounded-full mr-2"></div>
                        Revenus
                    </div>
                </div>
            </div>
            <div class="h-64">
                <canvas id="revenueChart"></canvas>
            </div>
        </div>

        <!-- Top Products -->
        <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-bold text-gray-900 dark:text-white flex items-center">
                    <i class="fas fa-star mr-3 text-yellow-500"></i>
                    Produits Populaires
                </h3>
                <select class="text-sm border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-1 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    <option>Aujourd'hui</option>
                    <option>Cette semaine</option>
                    <option>Ce mois</option>
                </select>
            </div>
            
            <div class="space-y-4">
                {% for product in top_products %}
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-lg flex items-center justify-center text-white font-bold text-sm">
                            {{ forloop.counter }}
                        </div>
                        <div>
                            <div class="font-medium text-gray-900 dark:text-white">{{ product.product__name }}</div>
                            <div class="text-sm text-gray-500 dark:text-gray-400">{{ product.total_quantity }} vendus</div>
                        </div>
                    </div>
                    <div class="w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div class="bg-gradient-to-r from-yellow-400 to-orange-500 h-2 rounded-full" style="width: {{ product.total_quantity|floatformat:0 }}%"></div>
                    </div>
                </div>
                {% empty %}
                <div class="text-center py-8">
                    <i class="fas fa-utensils text-gray-400 text-3xl mb-2"></i>
                    <p class="text-gray-500 dark:text-gray-400">Aucune vente aujourd'hui</p>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Table Performance -->
    <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
        <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-6 flex items-center">
            <i class="fas fa-chair mr-3 text-indigo-500"></i>
            Performance des Tables
        </h3>
        
        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-4">
            {% for table_stat in table_stats %}
            <div class="relative group">
                <div class="text-center p-4 rounded-xl transition-all duration-300 hover:scale-105
                    {% if table_stat.orders_count > 5 %}bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border-2 border-green-200 dark:border-green-700
                    {% elif table_stat.orders_count > 2 %}bg-gradient-to-br from-yellow-50 to-amber-50 dark:from-yellow-900/20 dark:to-amber-900/20 border-2 border-yellow-200 dark:border-yellow-700
                    {% else %}bg-gradient-to-br from-gray-50 to-slate-50 dark:from-gray-800 dark:to-slate-800 border-2 border-gray-200 dark:border-gray-700{% endif %}">
                    
                    <div class="w-12 h-12 mx-auto mb-3 rounded-xl flex items-center justify-center font-bold text-lg
                        {% if table_stat.orders_count > 5 %}bg-gradient-to-br from-green-500 to-emerald-600 text-white
                        {% elif table_stat.orders_count > 2 %}bg-gradient-to-br from-yellow-500 to-amber-600 text-white
                        {% else %}bg-gradient-to-br from-gray-400 to-slate-500 text-white{% endif %}">
                        {{ table_stat.number }}
                    </div>
                    
                    <div class="text-xs text-gray-600 dark:text-gray-400 mb-1">{{ table_stat.orders_count }} commandes</div>
                    <div class="font-bold text-gray-900 dark:text-white">{{ table_stat.revenue|floatformat:0 }} DH</div>
                    
                    <!-- Performance Indicator -->
                    <div class="mt-2">
                        {% if table_stat.orders_count > 5 %}
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300">
                            <i class="fas fa-fire mr-1"></i>Top
                        </span>
                        {% elif table_stat.orders_count > 2 %}
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300">
                            <i class="fas fa-chart-line mr-1"></i>Actif
                        </span>
                        {% else %}
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300">
                            <i class="fas fa-minus mr-1"></i>Calme
                        </span>
                        {% endif %}
                    </div>
                </div>
                
                <!-- Tooltip on hover -->
                <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none whitespace-nowrap z-10">
                    Table {{ table_stat.number }}: {{ table_stat.orders_count }} commandes, {{ table_stat.revenue|floatformat:0 }} DH
                    <div class="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900"></div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
        <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-6 flex items-center">
            <i class="fas fa-bolt mr-3 text-yellow-500"></i>
            Actions Rapides
        </h3>
        
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <a href="{% url 'restaurant:product_add' %}" class="group flex flex-col items-center p-6 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl hover:from-blue-100 hover:to-indigo-100 dark:hover:from-blue-900/30 dark:hover:to-indigo-900/30 transition-all duration-300 hover:scale-105 hover:shadow-lg">
                <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mb-3 group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-plus text-white text-lg"></i>
                </div>
                <span class="font-medium text-gray-900 dark:text-white">Ajouter Produit</span>
                <span class="text-xs text-gray-500 dark:text-gray-400 mt-1">Nouveau plat</span>
            </a>
            
            <a href="{% url 'restaurant:table_add' %}" class="group flex flex-col items-center p-6 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl hover:from-green-100 hover:to-emerald-100 dark:hover:from-green-900/30 dark:hover:to-emerald-900/30 transition-all duration-300 hover:scale-105 hover:shadow-lg">
                <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center mb-3 group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-chair text-white text-lg"></i>
                </div>
                <span class="font-medium text-gray-900 dark:text-white">Ajouter Table</span>
                <span class="text-xs text-gray-500 dark:text-gray-400 mt-1">Avec QR code</span>
            </a>
            
            <a href="{% url 'restaurant:category_add' %}" class="group flex flex-col items-center p-6 bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-xl hover:from-purple-100 hover:to-pink-100 dark:hover:from-purple-900/30 dark:hover:to-pink-900/30 transition-all duration-300 hover:scale-105 hover:shadow-lg">
                <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center mb-3 group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-tags text-white text-lg"></i>
                </div>
                <span class="font-medium text-gray-900 dark:text-white">Ajouter Catégorie</span>
                <span class="text-xs text-gray-500 dark:text-gray-400 mt-1">Organisation</span>
            </a>
            
            <a href="{% url 'restaurant:restaurant_settings' %}" class="group flex flex-col items-center p-6 bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 rounded-xl hover:from-orange-100 hover:to-red-100 dark:hover:from-orange-900/30 dark:hover:to-red-900/30 transition-all duration-300 hover:scale-105 hover:shadow-lg">
                <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-600 rounded-xl flex items-center justify-center mb-3 group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-cog text-white text-lg"></i>
                </div>
                <span class="font-medium text-gray-900 dark:text-white">Paramètres</span>
                <span class="text-xs text-gray-500 dark:text-gray-400 mt-1">Configuration</span>
            </a>
        </div>
    </div>
</div>

<!-- Enhanced JavaScript for Charts and Real-time Updates -->
<script>
    // Update current time
    function updateTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('fr-FR', { 
            hour: '2-digit', 
            minute: '2-digit' 
        });
        document.getElementById('currentTime').textContent = timeString;
    }
    
    // Update time every minute
    setInterval(updateTime, 60000);
    
    // Revenue Chart
    const revenueCtx = document.getElementById('revenueChart');
    if (revenueCtx) {
        new Chart(revenueCtx, {
            type: 'line',
            data: {
                labels: {{ revenue_labels|safe }},
                datasets: [{
                    label: 'Revenus (DH)',
                    data: {{ revenue_data|safe }},
                    borderColor: 'rgb(147, 51, 234)',
                    backgroundColor: 'rgba(147, 51, 234, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: 'rgb(147, 51, 234)',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8,
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)',
                        },
                        ticks: {
                            callback: function(value) {
                                return value + ' DH';
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false,
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index',
                },
                elements: {
                    point: {
                        hoverBackgroundColor: 'rgb(147, 51, 234)',
                    }
                }
            }
        });
    }
    
    // Auto-refresh dashboard data every 30 seconds
    setInterval(function() {
        // Add subtle refresh indicator
        const refreshIndicator = document.createElement('div');
        refreshIndicator.className = 'fixed top-4 right-4 z-50 bg-blue-500 text-white px-3 py-1 rounded-full text-xs flex items-center shadow-lg';
        refreshIndicator.innerHTML = '<i class="fas fa-sync-alt animate-spin mr-2"></i>Actualisation...';
        document.body.appendChild(refreshIndicator);
        
        // Remove indicator after 2 seconds
        setTimeout(() => {
            refreshIndicator.remove();
        }, 2000);
        
        // Here you would typically make an AJAX call to refresh data
        // For now, we'll just show the indicator
    }, 30000);
    
    // Add smooth scroll behavior to quick action links
    document.querySelectorAll('a[href^="{% url"]').forEach(link => {
        link.addEventListener('click', function(e) {
            // Add loading state
            const icon = this.querySelector('i');
            if (icon) {
                icon.classList.add('animate-spin');
            }
        });
    });
    
    // Initialize tooltips and animations
    document.addEventListener('DOMContentLoaded', function() {
        // Animate counters on page load
        const counters = document.querySelectorAll('.text-2xl.font-bold');
        counters.forEach(counter => {
            const target = parseInt(counter.textContent);
            let current = 0;
            const increment = target / 50;
            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    counter.textContent = target;
                    clearInterval(timer);
                } else {
                    counter.textContent = Math.floor(current);
                }
            }, 20);
        });
    });
</script>
{% endblock %}