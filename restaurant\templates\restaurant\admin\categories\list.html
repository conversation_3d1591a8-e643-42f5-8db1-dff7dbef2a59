{% extends 'restaurant/admin/base.html' %}

{% block title %}Categories - RestoQR Admin{% endblock %}
{% block page_title %}Categories{% endblock %}

{% block admin_content %}
<div class="space-y-6 fade-in">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div class="flex items-center space-x-4 mb-4 sm:mb-0">
            <h1 class="text-2xl font-bold text-gray-900 dark:text-light_text">Categories</h1>
            <span class="bg-primary text-white px-3 py-1 rounded-full text-sm font-medium">
                {{ categories.count }} total
            </span>
        </div>
        <a href="{% url 'restaurant:category_add' %}" class="btn-primary">
            <i class="fas fa-plus mr-2"></i>
            Add New Category
        </a>
    </div>
    
    <!-- Categories Table -->
    <div class="bg-white dark:bg-dark_card rounded-xl shadow-sm overflow-hidden">
        {% if categories %}
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Name
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Description
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-dark_card divide-y divide-gray-200 dark:divide-gray-700">
                    {% for category in categories %}
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900 dark:text-light_text">
                                {{ category.name }}
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm text-gray-500 dark:text-gray-400 line-clamp-2">
                                {{ category.description|default:"N/A" }}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                            <a href="{% url 'restaurant:category_edit' category.id %}" 
                               class="text-primary hover:text-secondary">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a href="{% url 'restaurant:category_delete' category.id %}" 
                               class="text-red-600 hover:text-red-800" 
                               onclick="return confirm(\'Are you sure you want to delete this category?\');">
                                <i class="fas fa-trash"></i>
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-12">
            <i class="fas fa-tags text-gray-400 text-6xl mb-4"></i>
            <h3 class="text-xl font-medium text-gray-900 dark:text-light_text mb-2">No categories found</h3>
            <p class="text-gray-500 dark:text-gray-400">Add new categories to organize your products.</p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

