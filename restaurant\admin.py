from django.contrib import admin
from .models import Category, Product, Table, Order, OrderItem, RestaurantSettings, AdminProfile


@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'created_at']
    search_fields = ['name']


@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    list_display = ['name', 'category', 'price', 'preparation_time', 'is_available', 'created_at']
    list_filter = ['category', 'is_available', 'created_at']
    search_fields = ['name', 'description']
    list_editable = ['price', 'is_available']


@admin.register(Table)
class TableAdmin(admin.ModelAdmin):
    list_display = ['number', 'capacity', 'is_active', 'created_at']
    list_filter = ['is_active']
    ordering = ['number']


class OrderItemInline(admin.TabularInline):
    model = OrderItem
    extra = 0
    readonly_fields = ['subtotal']


@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    list_display = ['id', 'table', 'status', 'total_amount', 'created_at']
    list_filter = ['status', 'created_at', 'table']
    search_fields = ['id', 'table__number']
    readonly_fields = ['id', 'total_amount', 'created_at', 'updated_at']
    inlines = [OrderItemInline]


@admin.register(RestaurantSettings)
class RestaurantSettingsAdmin(admin.ModelAdmin):
    list_display = ['name', 'phone', 'email']
    
    def has_add_permission(self, request):
        # Only allow one settings instance
        return not RestaurantSettings.objects.exists()


@admin.register(AdminProfile)
class AdminProfileAdmin(admin.ModelAdmin):
    list_display = ['user', 'phone', 'dark_mode', 'language']
    list_filter = ['dark_mode', 'language']

