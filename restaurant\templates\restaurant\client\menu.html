{% extends 'restaurant/base.html' %}

{% block title %}Menu - Table {{ table.number }} - RestoQR{% endblock %}

{% block extra_head %}
<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<style>
    .product-card {
        transition: all 0.3s ease;
    }
    .product-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    }
    .add-to-cart {
        transition: all 0.2s ease;
    }
    .add-to-cart:hover {
        transform: scale(1.05);
    }
    .cart-item-enter {
        animation: slideInRight 0.3s ease;
    }
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    .loading-spinner {
        border: 2px solid #f3f3f3;
        border-top: 2px solid #3B82F6;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        animation: spin 1s linear infinite;
    }
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Header -->
    <div class="bg-white dark:bg-dark_card shadow-sm sticky top-0 z-50">
        <div class="max-w-4xl mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                        <i class="fas fa-utensils text-white"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-gray-900 dark:text-light_text">RestoQR</h1>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Table {{ table.number }}</p>
                    </div>
                </div>
                
                <button id="cart-btn" class="relative bg-primary text-white px-4 py-2 rounded-lg hover:bg-secondary transition-colors duration-200">
                    <i class="fas fa-shopping-cart mr-2"></i>
                    Cart
                    <span id="cart-count" class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-6 h-6 flex items-center justify-center hidden">0</span>
                </button>
            </div>
        </div>
    </div>
    
    <!-- Menu Content -->
    <div class="max-w-4xl mx-auto px-4 py-6">
        <!-- Welcome Message -->
        <div class="bg-white dark:bg-dark_card rounded-xl shadow-sm p-6 mb-6">
            <h2 class="text-2xl font-bold text-gray-900 dark:text-light_text mb-2">
                Welcome to Table {{ table.number }}!
            </h2>
            <p class="text-gray-600 dark:text-gray-400">
                Browse our delicious menu and place your order directly from your phone. 
                Your order will be sent to our kitchen immediately.
            </p>
        </div>
        
        <!-- Category Filter -->
        <div class="mb-6">
            <div class="flex space-x-2 overflow-x-auto pb-2">
                <button class="category-filter active whitespace-nowrap px-4 py-2 bg-primary text-white rounded-lg hover:bg-secondary transition-colors duration-200" data-category="all">
                    All Items
                </button>
                {% for category in categories %}
                <button class="category-filter whitespace-nowrap px-4 py-2 bg-white dark:bg-dark_card text-gray-700 dark:text-light_text rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200" data-category="{{ category.id }}">
                    {{ category.name }}
                </button>
                {% endfor %}
            </div>
        </div>
        
        <!-- Menu Items -->
        <div class="space-y-6">
            {% for category in categories %}
            <div class="category-section" data-category="{{ category.id }}">
                <h3 class="text-xl font-bold text-gray-900 dark:text-light_text mb-4 flex items-center">                    <i class="fas fa-{{ category.icon|default:"utensils" }} mr-2 text-primary"></i>
                    {{ category.name }}
                </h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {% for product in category.products.all %}
                    {% if product.is_available %}
                    <div class="product-card bg-white dark:bg-dark_card rounded-xl shadow-sm p-4 hover:shadow-md transition-shadow duration-200">
                        <!-- Product Image -->
                        <div class="w-full h-32 bg-gray-200 dark:bg-gray-700 rounded-lg mb-4 flex items-center justify-center">
                            {% if product.image %}
                                <img src="{{ product.image.url }}" alt="{{ product.name }}" class="w-full h-full object-cover rounded-lg">
                            {% else %}
                                <i class="fas fa-image text-gray-400 text-3xl"></i>
                            {% endif %}
                        </div>
                        
                        <!-- Product Info -->
                        <div class="space-y-2">
                            <div class="flex items-start justify-between">
                                <h4 class="font-semibold text-gray-900 dark:text-light_text">{{ product.name }}</h4>
                                <span class="text-lg font-bold text-primary">{{ product.price }} dh</span>
                            </div>
                            
                            <p class="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                                {{ product.description }}
                            </p>
                            
                            <div class="flex items-center justify-between">
                                <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                                    <i class="fas fa-clock mr-1"></i>
                                    {{ product.prep_time }} min
                                </div>
                                
                                <button class="add-to-cart bg-primary text-white px-4 py-2 rounded-lg hover:bg-secondary transition-colors duration-200"
                                        data-product-id="{{ product.id }}"
                                        data-product-name="{{ product.name }}"
                                        data-product-price="{{ product.price }}">
                                    <i class="fas fa-plus mr-1"></i>
                                    Add
                                </button>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    {% endfor %}
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    
    <!-- Cart Sidebar -->
    <div id="cart-sidebar" class="fixed inset-y-0 right-0 w-80 bg-white dark:bg-dark_card shadow-xl transform translate-x-full transition-transform duration-300 z-50">
        <div class="flex flex-col h-full">
            <!-- Cart Header -->
            <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-light_text">Your Order</h3>
                    <button id="close-cart" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
                <p class="text-sm text-gray-600 dark:text-gray-400">Table {{ table.number }}</p>
            </div>
            
            <!-- Cart Items -->
            <div id="cart-items" class="flex-1 overflow-y-auto p-4 space-y-3">
                <div id="empty-cart" class="text-center py-8">
                    <i class="fas fa-shopping-cart text-gray-400 text-4xl mb-4"></i>
                    <p class="text-gray-500 dark:text-gray-400">Your cart is empty</p>
                    <p class="text-sm text-gray-400 dark:text-gray-500">Add items from the menu</p>
                </div>
            </div>
            
            <!-- Cart Footer -->
            <div id="cart-footer" class="p-4 border-t border-gray-200 dark:border-gray-700 hidden">
                <div class="space-y-4">
                    <!-- Order Notes -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-light_text mb-2">
                            Special Instructions (Optional)
                        </label>
                        <textarea id="order-notes" 
                                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-light_text"
                                  rows="3" 
                                  placeholder="Any special requests or dietary requirements..."></textarea>
                    </div>
                    
                    <!-- Total -->
                    <div class="flex items-center justify-between text-lg font-semibold">
                        <span class="text-gray-900 dark:text-light_text">Total:</span>
                        <span id="cart-total" class="text-primary">0.00 dh</span>
                    </div>
                    
                    <!-- Place Order Button -->
                    <button id="place-order" class="w-full bg-primary text-white py-3 rounded-lg hover:bg-secondary transition-colors duration-200 font-semibold">
                        <i class="fas fa-paper-plane mr-2"></i>
                        Place Order
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Cart Overlay -->
    <div id="cart-overlay" class="fixed inset-0 bg-black bg-opacity-50 hidden z-40"></div>
</div>

<script>
    // Cart functionality
    let cart = [];
    let cartTotal = 0;
    
    // DOM elements
    const cartBtn = document.getElementById('cart-btn');
    const cartSidebar = document.getElementById('cart-sidebar');
    const cartOverlay = document.getElementById('cart-overlay');
    const closeCartBtn = document.getElementById('close-cart');
    const cartItems = document.getElementById('cart-items');
    const cartFooter = document.getElementById('cart-footer');
    const emptyCart = document.getElementById('empty-cart');
    const cartCount = document.getElementById('cart-count');
    const cartTotalElement = document.getElementById('cart-total');
    const placeOrderBtn = document.getElementById('place-order');
    const orderNotes = document.getElementById('order-notes');
    
    // Category filter functionality
    const categoryFilters = document.querySelectorAll('.category-filter');
    const categorySections = document.querySelectorAll('.category-section');
    
    categoryFilters.forEach(filter => {
        filter.addEventListener('click', () => {
            const category = filter.dataset.category;
            
            // Update active filter
            categoryFilters.forEach(f => f.classList.remove('active', 'bg-primary', 'text-white'));
            categoryFilters.forEach(f => f.classList.add('bg-white', 'dark:bg-gray-800', 'text-gray-700', 'dark:text-gray-300'));
            filter.classList.add('active', 'bg-primary', 'text-white');
            filter.classList.remove('bg-white', 'dark:bg-gray-800', 'text-gray-700', 'dark:text-gray-300');
            
            // Show/hide categories
            if (category === 'all') {
                categorySections.forEach(section => section.style.display = 'block');
            } else {
                categorySections.forEach(section => {
                    section.style.display = section.dataset.category === category ? 'block' : 'none';
                });
            }
        });
    });
    
    // Add to cart functionality
    const addToCartBtns = document.querySelectorAll('.add-to-cart');
    addToCartBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            const productId = btn.dataset.productId;
            const productName = btn.dataset.productName;
            const productPrice = parseFloat(btn.dataset.productPrice);
            
            addToCart(productId, productName, productPrice);
        });
    });
    
    function addToCart(productId, productName, productPrice) {
        const existingItem = cart.find(item => item.id === productId);
        
        if (existingItem) {
            existingItem.quantity += 1;
        } else {
            cart.push({
                id: productId,
                name: productName,
                price: productPrice,
                quantity: 1
            });
        }
        
        // Show success animation
        const Toast = Swal.mixin({
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
        });
        
        Toast.fire({
            icon: 'success',
            title: `${productName} added to cart!`
        });
        
        updateCart();
    }
    
    function removeFromCart(productId) {
        cart = cart.filter(item => item.id !== productId);
        updateCart();
    }
    
    function updateQuantity(productId, quantity) {
        const item = cart.find(item => item.id === productId);
        if (item) {
            item.quantity = Math.max(0, quantity);
            if (item.quantity === 0) {
                removeFromCart(productId);
            } else {
                updateCart();
            }
        }
    }
    
    function updateCart() {
        // Update cart count
        const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
        cartCount.textContent = totalItems;
        cartCount.classList.toggle('hidden', totalItems === 0);
        
        // Update cart total
        cartTotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        cartTotalElement.textContent = `${cartTotal.toFixed(2)} dh`;
        
        // Update cart items display
        if (cart.length === 0) {
            emptyCart.style.display = 'block';
            cartFooter.classList.add('hidden');
            cartItems.innerHTML = '<div id="empty-cart" class="text-center py-8"><i class="fas fa-shopping-cart text-gray-400 text-4xl mb-4"></i><p class="text-gray-500 dark:text-gray-400">Your cart is empty</p><p class="text-sm text-gray-400 dark:text-gray-500">Add items from the menu</p></div>';
        } else {
            emptyCart.style.display = 'none';
            cartFooter.classList.remove('hidden');
            
            cartItems.innerHTML = cart.map(item => `
                <div class="flex items-center justify-between bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                    <div class="flex-1">
                        <h4 class="font-medium text-gray-900 dark:text-white">${item.name}</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">${item.price.toFixed(2)} dh each</p>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button onclick="updateQuantity('${item.id}', ${item.quantity - 1})" class="w-8 h-8 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center hover:bg-gray-300 dark:hover:bg-gray-500">
                            <i class="fas fa-minus text-xs"></i>
                        </button>
                        <span class="w-8 text-center font-medium text-gray-900 dark:text-white">${item.quantity}</span>
                        <button onclick="updateQuantity('${item.id}', ${item.quantity + 1})" class="w-8 h-8 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center hover:bg-gray-300 dark:hover:bg-gray-500">
                            <i class="fas fa-plus text-xs"></i>
                        </button>
                    </div>
                </div>
            `).join('');
        }
    }
    
    // Cart sidebar functionality
    cartBtn.addEventListener('click', () => {
        cartSidebar.classList.remove('translate-x-full');
        cartOverlay.classList.remove('hidden');
    });
    
    closeCartBtn.addEventListener('click', closeCart);
    cartOverlay.addEventListener('click', closeCart);
    
    function closeCart() {
        cartSidebar.classList.add('translate-x-full');
        cartOverlay.classList.add('hidden');
    }
    
    // Place order functionality
    placeOrderBtn.addEventListener('click', async () => {
        if (cart.length === 0) return;
        
        const orderData = {
            table_id: {{ table.id }},
            items: cart,
            notes: orderNotes.value.trim()
        };
        
        try {
            placeOrderBtn.disabled = true;
            placeOrderBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Placing Order...';
            
            const response = await fetch('/api/place-order/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                },
                body: JSON.stringify(orderData)
            });
            
            if (response.ok) {
                const result = await response.json();
                // Redirect to thank you page
                window.location.href = `/qr/{{ table.id }}/thank-you/?order=${result.order_id}`;
            } else {
                throw new Error("Failed to place order");
            }
        } catch (error) {
            alert("Sorry, there was an error placing your order. Please try again.");
            placeOrderBtn.disabled = false;
            placeOrderBtn.innerHTML = 
                `<i class="fas fa-paper-plane mr-2"></i>Place Order`;
        }
    });
    
    // Add CSRF token to all requests
    const csrfToken = '{{ csrf_token }}';
    
    // Initialize cart
    updateCart();
</script>

<!-- CSRF Token -->
{% csrf_token %}
{% endblock %}

