from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from restaurant.models import RestaurantSettings, AdminProfile, Category, Product, Table
from decimal import Decimal


class Command(BaseCommand):
    help = 'Setup initial admin user and sample data for RestoQR'

    def handle(self, *args, **options):
        # Create superuser if it doesn't exist
        if not User.objects.filter(username='admin').exists():
            admin_user = User.objects.create_superuser(
                username='admin',
                email='<EMAIL>',
                password='admin123',
                first_name='Admin',
                last_name='User'
            )
            admin_user.is_staff = True
            admin_user.save()
            
            # Create admin profile
            AdminProfile.objects.create(
                user=admin_user,
                phone='+212 6 00 00 00 00',
                dark_mode=False,
                language='fr'
            )
            
            self.stdout.write(
                self.style.SUCCESS('✅ Admin user created: admin / admin123')
            )
        else:
            self.stdout.write(
                self.style.WARNING('⚠️  Admin user already exists')
            )

        # Create restaurant settings
        settings, created = RestaurantSettings.objects.get_or_create(
            defaults={
                'name': 'RestoQR Restaurant',
                'primary_color': '#3B82F6',
                'secondary_color': '#1E40AF',
                'accent_color': '#F59E0B',
                'phone': '+212 5 22 00 00 00',
                'email': '<EMAIL>',
                'address': '123 Avenue Mohammed V, Casablanca, Maroc',
                'currency_symbol': 'dh',
                'tax_rate': Decimal('10.00'),
                'service_charge': Decimal('5.00'),
            }
        )
        
        if created:
            self.stdout.write(
                self.style.SUCCESS('✅ Restaurant settings created')
            )

        # Create sample categories
        categories_data = [
            {'name': 'Pizzas', 'description': 'Delicious wood-fired pizzas'},
            {'name': 'Burgers', 'description': 'Juicy gourmet burgers'},
            {'name': 'Salads', 'description': 'Fresh and healthy salads'},
            {'name': 'Beverages', 'description': 'Refreshing drinks'},
            {'name': 'Desserts', 'description': 'Sweet treats to end your meal'},
        ]
        
        created_categories = []
        for cat_data in categories_data:
            category, created = Category.objects.get_or_create(
                name=cat_data['name'],
                defaults={'description': cat_data['description']}
            )
            if created:
                created_categories.append(category.name)
        
        if created_categories:
            self.stdout.write(
                self.style.SUCCESS(f'✅ Categories created: {", ".join(created_categories)}')
            )

        # Create sample products
        if Category.objects.exists():
            pizza_cat = Category.objects.get(name='Pizzas')
            burger_cat = Category.objects.get(name='Burgers')
            salad_cat = Category.objects.get(name='Salads')
            beverage_cat = Category.objects.get(name='Beverages')
            dessert_cat = Category.objects.get(name='Desserts')
            
            products_data = [
                # Pizzas
                {'name': 'Margherita Pizza', 'description': 'Classic pizza with tomato sauce, mozzarella, and fresh basil', 'price': '120.00', 'category': pizza_cat, 'prep_time': 15},
                {'name': 'Pepperoni Pizza', 'description': 'Traditional pizza with pepperoni and mozzarella cheese', 'price': '140.00', 'category': pizza_cat, 'prep_time': 15},
                {'name': 'Vegetarian Pizza', 'description': 'Loaded with fresh vegetables and cheese', 'price': '130.00', 'category': pizza_cat, 'prep_time': 18},
                
                # Burgers
                {'name': 'Classic Cheeseburger', 'description': 'Beef patty with cheese, lettuce, tomato, and special sauce', 'price': '95.00', 'category': burger_cat, 'prep_time': 12},
                {'name': 'BBQ Bacon Burger', 'description': 'Beef patty with bacon, BBQ sauce, and onion rings', 'price': '110.00', 'category': burger_cat, 'prep_time': 15},
                {'name': 'Veggie Burger', 'description': 'Plant-based patty with fresh vegetables', 'price': '85.00', 'category': burger_cat, 'prep_time': 10},
                
                # Salads
                {'name': 'Caesar Salad', 'description': 'Crisp romaine lettuce with Caesar dressing and croutons', 'price': '75.00', 'category': salad_cat, 'prep_time': 5},
                {'name': 'Greek Salad', 'description': 'Fresh vegetables with feta cheese and olive oil', 'price': '85.00', 'category': salad_cat, 'prep_time': 7},
                
                # Beverages
                {'name': 'Coca Cola', 'description': 'Classic refreshing cola drink', 'price': '25.00', 'category': beverage_cat, 'prep_time': 1},
                {'name': 'Fresh Orange Juice', 'description': 'Freshly squeezed orange juice', 'price': '35.00', 'category': beverage_cat, 'prep_time': 3},
                {'name': 'Coffee', 'description': 'Freshly brewed coffee', 'price': '20.00', 'category': beverage_cat, 'prep_time': 5},
                
                # Desserts
                {'name': 'Chocolate Cake', 'description': 'Rich chocolate cake with chocolate frosting', 'price': '55.00', 'category': dessert_cat, 'prep_time': 2},
                {'name': 'Ice Cream Sundae', 'description': 'Vanilla ice cream with chocolate sauce and whipped cream', 'price': '45.00', 'category': dessert_cat, 'prep_time': 3},
            ]
            
            created_products = []
            for prod_data in products_data:
                product, created = Product.objects.get_or_create(
                    name=prod_data['name'],
                    defaults={
                        'description': prod_data['description'],
                        'price': Decimal(prod_data['price']),
                        'category': prod_data['category'],
                        'preparation_time': prod_data['prep_time'],
                        'is_available': True
                    }
                )
                if created:
                    created_products.append(product.name)
            
            if created_products:
                self.stdout.write(
                    self.style.SUCCESS(f'✅ Products created: {len(created_products)} items')
                )

        # Create sample tables
        created_tables = []
        for table_num in range(1, 11):  # Create tables 1-10
            table, created = Table.objects.get_or_create(
                number=table_num,
                defaults={
                    'capacity': 4,
                    'qr_code': f'table_{table_num}_{table_num}',
                    'is_active': True
                }
            )
            if created:
                created_tables.append(str(table_num))
        
        if created_tables:
            self.stdout.write(
                self.style.SUCCESS(f'✅ Tables created: {", ".join(created_tables)}')
            )

        self.stdout.write(
            self.style.SUCCESS('\n🎉 Setup completed successfully!')
        )
        self.stdout.write(
            self.style.SUCCESS('You can now login with: admin / admin123')
        )
        self.stdout.write(
            self.style.SUCCESS('Visit: http://localhost:8000/admin/login/')
        )