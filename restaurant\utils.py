from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
from django.conf import settings
from django.http import HttpResponse
from io import BytesIO
import os
from datetime import datetime

def generate_invoice_pdf(order):
    """
    Generate a PDF invoice for the given order
    """
    buffer = BytesIO()
    
    # Create the PDF document
    doc = SimpleDocTemplate(
        buffer,
        pagesize=A4,
        rightMargin=72,
        leftMargin=72,
        topMargin=72,
        bottomMargin=18
    )
    
    # Container for the 'Flowable' objects
    elements = []
    
    # Get styles
    styles = getSampleStyleSheet()
    
    # Custom styles
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=24,
        spaceAfter=30,
        alignment=TA_CENTER,
        textColor=colors.HexColor('#4F46E5')
    )
    
    header_style = ParagraphStyle(
        'CustomHeader',
        parent=styles['Heading2'],
        fontSize=16,
        spaceAfter=12,
        textColor=colors.HexColor('#1F2937')
    )
    
    normal_style = ParagraphStyle(
        'CustomNormal',
        parent=styles['Normal'],
        fontSize=10,
        spaceAfter=6
    )
    
    # Title
    title = Paragraph("INVOICE", title_style)
    elements.append(title)
    elements.append(Spacer(1, 12))
    
    # Restaurant Info
    restaurant_info = [
        ["RestoQR Restaurant", ""],
        ["123 Avenue Mohammed V", f"Invoice #: {order.id}"],
        ["Casablanca, Maroc", f"Date: {order.created_at.strftime('%B %d, %Y')}"],
        ["Phone: +212 5 22 00 00 00", f"Time: {order.created_at.strftime('%I:%M %p')}"],
        ["Email: <EMAIL>", f"Table: {order.table.number}"]
    ]
    
    restaurant_table = Table(restaurant_info, colWidths=[3*inch, 2.5*inch])
    restaurant_table.setStyle(TableStyle([
        ('ALIGN', (0, 0), (0, -1), 'LEFT'),
        ('ALIGN', (1, 0), (1, -1), 'RIGHT'),
        ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 0), (-1, -1), 10),
        ('FONTNAME', (0, 0), (0, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (0, 0), 14),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
    ]))
    
    elements.append(restaurant_table)
    elements.append(Spacer(1, 20))
    
    # Order Details Header
    order_header = Paragraph("Order Details", header_style)
    elements.append(order_header)
    
    # Order Items Table
    data = [['Item', 'Qty', 'Price', 'Total']]
    
    for item in order.items.all():
        data.append([
            item.product.name,
            str(item.quantity),
            f"{item.product.price:.2f} dh",
            f"{item.subtotal:.2f} dh"
        ])
    
    # Add subtotal, tax, and total
    data.append(['', '', '', ''])  # Empty row
    data.append(['', '', 'Subtotal:', f"{order.total_amount:.2f} dh"])
    data.append(['', '', 'Tax (0%):', '0.00 dh'])
    data.append(['', '', 'Total:', f"{order.total_amount:.2f} dh"])
    
    # Create table
    items_table = Table(data, colWidths=[3*inch, 0.8*inch, 1*inch, 1*inch])
    items_table.setStyle(TableStyle([
        # Header row
        ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#4F46E5')),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 12),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        
        # Data rows
        ('FONTNAME', (0, 1), (-1, -4), 'Helvetica'),
        ('FONTSIZE', (0, 1), (-1, -4), 10),
        ('ALIGN', (0, 1), (0, -4), 'LEFT'),  # Item names left-aligned
        ('GRID', (0, 0), (-1, -4), 1, colors.black),
        
        # Totals section
        ('FONTNAME', (0, -3), (-1, -1), 'Helvetica-Bold'),
        ('FONTSIZE', (0, -3), (-1, -1), 10),
        ('ALIGN', (2, -3), (-1, -1), 'RIGHT'),
        ('LINEABOVE', (2, -3), (-1, -3), 1, colors.black),
        ('LINEABOVE', (2, -1), (-1, -1), 2, colors.black),
    ]))
    
    elements.append(items_table)
    elements.append(Spacer(1, 20))
    
    # Special Instructions
    if order.customer_notes:
        notes_header = Paragraph("Special Instructions", header_style)
        elements.append(notes_header)
        notes_text = Paragraph(order.customer_notes, normal_style)
        elements.append(notes_text)
        elements.append(Spacer(1, 20))
    
    # Order Status
    status_header = Paragraph("Order Status", header_style)
    elements.append(status_header)
    
    status_info = [
        ["Status:", order.get_status_display()],
    ]
    
    if order.estimated_ready_time:
        status_info.append(["Estimated Ready:", order.estimated_ready_time.strftime('%I:%M %p')])
    
    status_table = Table(status_info, colWidths=[2*inch, 3*inch])
    status_table.setStyle(TableStyle([
        ('ALIGN', (0, 0), (0, -1), 'LEFT'),
        ('ALIGN', (1, 0), (1, -1), 'LEFT'),
        ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
        ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 0), (-1, -1), 10),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
    ]))
    
    elements.append(status_table)
    elements.append(Spacer(1, 30))
    
    # Footer
    footer_text = Paragraph(
        "Thank you for dining with us!<br/>"
        "For any questions about your order, please contact our staff.<br/>"
        "<br/>"
        "RestoQR - Digital Restaurant Experience",
        ParagraphStyle(
            'Footer',
            parent=styles['Normal'],
            fontSize=9,
            alignment=TA_CENTER,
            textColor=colors.grey
        )
    )
    elements.append(footer_text)
    
    # Build PDF
    doc.build(elements)
    
    # Get the value of the BytesIO buffer and return it
    pdf = buffer.getvalue()
    buffer.close()
    
    return pdf

def create_invoice_response(order):
    """
    Create an HTTP response with the PDF invoice
    """
    pdf_data = generate_invoice_pdf(order)
    
    response = HttpResponse(pdf_data, content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="invoice-{order.id}.pdf"'
    
    return response

def save_invoice_pdf(order, file_path=None):
    """
    Save the PDF invoice to a file
    """
    if not file_path:
        # Create invoices directory if it doesn't exist
        invoices_dir = os.path.join(settings.MEDIA_ROOT, 'invoices')
        os.makedirs(invoices_dir, exist_ok=True)
        file_path = os.path.join(invoices_dir, f'invoice-{order.id}.pdf')
    
    pdf_data = generate_invoice_pdf(order)
    
    with open(file_path, 'wb') as f:
        f.write(pdf_data)
    
    return file_path

