/* General Body Styles */
body {
    font-family: 'Inter', sans-serif; /* Modern sans-serif font */
    background-color: #f8f9fa; /* Light gray background */
    color: #343a40; /* Dark gray text */
    line-height: 1.6;
}

/* Headings */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Montserrat', sans-serif; /* A bolder, more modern heading font */
    color: #212529; /* Even darker gray for headings */
    margin-bottom: 0.5em;
}

/* Buttons */
.btn {
    border-radius: 0.3rem; /* Slightly rounded corners */
    padding: 0.75rem 1.25rem;
    font-weight: 600;
    transition: all 0.3s ease; /* Smooth transitions for hover effects */
}

.btn-primary {
    background-color: #4F46E5; /* Primary brand color */
    border-color: #4F46E5;
}

.btn-primary:hover {
    background-color: #3b34b3;
    border-color: #3b34b3;
}

/* Cards/Containers */
.card {
    border-radius: 0.75rem; /* More rounded corners for cards */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05); /* Subtle shadow */
    border: none; /* Remove default border */
}

/* Forms */
.form-control {
    border-radius: 0.3rem;
    border: 1px solid #ced4da;
    padding: 0.75rem 1rem;
}

.form-control:focus {
    border-color: #4F46E5;
    box-shadow: 0 0 0 0.2rem rgba(79, 70, 229, 0.25);
}

/* Navigation/Sidebar (if applicable) */
.sidebar {
    background-color: #343a40; /* Dark sidebar background */
    color: #f8f9fa;
}

.sidebar .nav-link {
    color: #adb5bd; /* Lighter text for nav links */
}

.sidebar .nav-link.active,
.sidebar .nav-link:hover {
    color: #ffffff;
    background-color: #495057; /* Highlight active/hovered link */
}

/* Specific elements from existing templates */
/* Adjustments for dashboard */
.dashboard-card {
    text-align: center;
    padding: 20px;
}

.dashboard-card h5 {
    font-size: 1.2rem;
    margin-bottom: 10px;
}

.dashboard-card .display-4 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #4F46E5;
}

/* Adjustments for tables/products list */
.product-item, .table-item {
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    margin-bottom: 15px;
    padding: 15px;
    background-color: #ffffff;
}

.product-item h5, .table-item h5 {
    color: #4F46E5;
}

.product-item .price {
    font-weight: 700;
    color: #28a745; /* Green for price */
}

/* Client Menu Specifics */
.menu-item-card {
    border-radius: 0.75rem;
    overflow: hidden;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.menu-item-card img {
    border-top-left-radius: 0.75rem;
    border-top-right-radius: 0.75rem;
}

.menu-item-card .card-body {
    padding: 1.25rem;
}

.menu-item-card .item-price {
    font-size: 1.4rem;
    font-weight: 700;
    color: #4F46E5;
}

.category-title {
    color: #4F46E5;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 0.5rem;
    margin-bottom: 1.5rem;
}

/* QR Code Page */
.qr-code-container {
    background-color: #ffffff;
    padding: 30px;
    border-radius: 0.75rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.qr-code-container img {
    max-width: 100%;
    height: auto;
    margin-bottom: 20px;
}

.qr-code-container h2 {
    color: #4F46E5;
    margin-bottom: 15px;
}

.qr-code-container p {
    font-size: 1.1rem;
    color: #6c757d;
}

/* Utility classes for spacing and alignment */
.mb-4 { margin-bottom: 1.5rem !important; }
.mt-4 { margin-top: 1.5rem !important; }
.text-center { text-align: center !important; }
.d-flex { display: flex !important; }
.justify-content-center { justify-content: center !important; }
.align-items-center { align-items: center !important; }

/* Responsive adjustments */
@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        height: auto;
    }
    .container-fluid {
        padding: 15px;
    }
    .menu-item-card .card-body {
        padding: 1rem;
    }
    .menu-item-card .item-price {
        font-size: 1.2rem;
    }
}


